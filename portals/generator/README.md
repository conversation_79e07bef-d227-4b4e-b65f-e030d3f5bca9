# Generator Artykułów AI - OpenRouter Edition

Zaawansowany generator artykułów SEO wykorzystujący OpenRouter API, zaprojektowany do pracy w tle na serwerach VPS z obsługą wielu instancji w screen.

## 🚀 Funkcje

- **Automatyczne generowanie artykułów SEO** - minimum 1200 słów
- **Integracja z OpenRouter** - dostęp do najlepszych modeli AI
- **Praca w tle** - obsługa screen dla wielu instancji
- **Interaktywna konfiguracja** - łatwe ustawienie parametrów
- **Monitoring postępu** - szczegółowe logi i statystyki
- **Automatyczny zapis** - pliki HTML gotowe do WordPress
- **Obsługa błędów** - pomijanie problemowych artykułów
- **Konfigurowalny timing** - opóźnienia między artykułami

## 📋 Wymagania

- Python 3.6+
- screen (do pracy w tle)
- Klucz API OpenRouter
- Połączenie internetowe

## 🛠️ Instalacja

### 1. Przejdź do katalogu generatora
```bash
cd /www/portals/generator
```

### 2. Zainstaluj zależności Python
```bash
pip3 install -r requirements.txt
```

### 3. Nadaj uprawnienia wykonywania skryptowi
```bash
chmod +x run_generator.sh
```

## 🎯 Użycie

### Uruchomienie menedżera sesji
```bash
./run_generator.sh
```

### Bezpośrednie uruchomienie generatora
```bash
python3 generator.py
```

### Praca z screen (ręcznie)
```bash
# Uruchom nową sesję
screen -S generator_nazwa

# Uruchom generator
python3 generator.py

# Odłącz się od sesji (Ctrl+A, potem D)

# Dołącz do sesji
screen -r generator_nazwa

# Lista sesji
screen -ls
```

## ⚙️ Konfiguracja

### Pierwsze uruchomienie
1. **Klucz API OpenRouter** - wprowadź swój klucz API
2. **Wybór modelu** - wybierz model AI z listy dostępnych
3. **Folder wyjściowy** - ustaw nazwę folderu dla artykułów
4. **Parametry artykułów** - długość, ton, słowa kluczowe
5. **Timing** - opóźnienia między generowaniem

### Plik konfiguracyjny
Konfiguracja jest automatycznie zapisywana w `generator_config.json`:

```json
{
  "openrouter_api_key": "sk-or-v1-...",
  "model": "openai/gpt-4",
  "output_folder": "artykuly",
  "article_length": "long",
  "tone": "professional",
  "delay_minutes": 0,
  "delay_seconds": 30,
  "target_keywords": "sprzątanie, czyszczenie",
  "additional_info": "Kontakt: <EMAIL>"
}
```

## 📝 Dodawanie tytułów

### Metody dodawania:
1. **Po jednym tytule** - wprowadź tytuł i naciśnij Enter
2. **Masowo** - wklej listę tytułów oddzielonych średnikami (;)
3. **Zakończenie** - wpisz 'koniec' aby przejść dalej

### Przykład tytułów:
```
Jak skutecznie wysprzątać biuro po remoncie; 5 najlepszych metod czyszczenia dywanów; Profesjonalne sprzątanie - dlaczego warto zaufać ekspertom; Ekologiczne środki czyszczące - przewodnik 2024
```

## 🔧 Zarządzanie sesjami

### Menedżer sesji (run_generator.sh)
- **Uruchom nową sesję** - tworzy nową instancję generatora
- **Dołącz do sesji** - podłącza do istniejącej sesji
- **Pokaż aktywne sesje** - lista wszystkich działających generatorów
- **Zatrzymaj sesję** - bezpieczne zamknięcie wybranej sesji

### Przykład wielu instancji:
```bash
# Sesja 1: artykuły o sprzątaniu biur
screen -S generator_biura

# Sesja 2: artykuły o sprzątaniu domów
screen -S generator_domy

# Sesja 3: artykuły o czyszczeniu dywanów
screen -S generator_dywany
```

## 📊 Monitoring i logi

### Logi w czasie rzeczywistym
- **Postęp generowania** - pasek postępu z ETA
- **Status artykułów** - sukces/błąd dla każdego artykułu
- **Statystyki** - liczba wygenerowanych/nieudanych artykułów
- **Czas generowania** - średni czas na artykuł

### Plik logów
Wszystkie operacje są zapisywane w `generator.log`:
```
[2024-01-15 14:30:15] [INFO] Generuję artykuł 1/10: Jak skutecznie wysprzątać biuro
[2024-01-15 14:31:45] [SUCCESS] ✅ Artykuł zapisany: Jak skutecznie wysprzątać biuro
[2024-01-15 14:31:46] [INFO] Generuję artykuł 2/10: 5 najlepszych metod czyszczenia
```

## 📁 Struktura plików wyjściowych

```
artykuly/
├── jak-skutecznie-wysprzatac-biuro_20240115_143145.html
├── 5-najlepszych-metod-czyszczenia_20240115_143320.html
└── profesjonalne-sprzatanie-dlaczego_20240115_143455.html
```

### Format nazw plików:
- Bezpieczna nazwa z tytułu (bez znaków specjalnych)
- Timestamp (YYYYMMDD_HHMMSS)
- Rozszerzenie .html

## 🎨 Przykład wygenerowanego artykułu

```html
<!-- Meta description: Poznaj skuteczne metody sprzątania biura po remoncie. Praktyczne porady, narzędzia i techniki dla idealnej czystości. -->

<h1>Jak skutecznie wysprzątać biuro po remoncie</h1>

<p>Remont biura to inwestycja w przyszłość firmy, ale pozostawia po sobie...</p>

<h2>Przygotowanie do sprzątania</h2>
<p>Przed rozpoczęciem właściwego sprzątania należy...</p>

<ul>
<li>Zabezpieczenie sprzętu elektronicznego</li>
<li>Przygotowanie odpowiednich narzędzi</li>
<li>Planowanie kolejności prac</li>
</ul>

<!-- Dalszy ciąg artykułu... -->
```

## 🚨 Rozwiązywanie problemów

### Błędy API
- **Nieprawidłowy klucz** - sprawdź format klucza OpenRouter
- **Limit zapytań** - poczekaj lub zmień model
- **Błąd połączenia** - sprawdź internet

### Błędy systemu
- **Brak uprawnień** - `chmod +x run_generator.sh`
- **Brak screen** - `sudo apt install screen`
- **Brak Python** - zainstaluj Python 3.6+

### Błędy generowania
- **Artykuł za krótki** - generator automatycznie rozszerza
- **Błąd zapisu** - sprawdź uprawnienia do folderu
- **Przerwanie** - użyj Ctrl+C dla bezpiecznego zatrzymania

## 💡 Wskazówki

### Optymalizacja wydajności
- Używaj modeli GPT-3.5 dla szybkości
- Ustaw opóźnienia 30-60s między artykułami
- Monitoruj zużycie API

### Jakość artykułów
- Używaj konkretnych słów kluczowych
- Dodaj informacje o firmie
- Wybierz odpowiedni ton dla grupy docelowej

### Zarządzanie sesjami
- Używaj opisowych nazw sesji
- Regularnie sprawdzaj logi
- Zatrzymuj nieużywane sesje

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi w `generator.log`
2. Zweryfikuj konfigurację w `generator_config.json`
3. Przetestuj połączenie z API
4. Sprawdź dostępne modele

---

**Autor:** AI Assistant  
**Wersja:** 1.0  
**Data:** 2024-01-15
