#!/bin/bash

# Generator Artykułów AI - Skrypt uruchamiający
# Autor: AI Assistant
# Data: $(date +%Y-%m-%d)

# Kolory dla lepszej czytelności
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funkcja wyświetlająca nagłówek
show_header() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    GENERATOR ARTYKUŁÓW AI                    ║"
    echo "║                     Screen Manager                           ║"
    echo "║                                                              ║"
    echo "║  🚀 Uruchamianie generatora w screen                        ║"
    echo "║  📊 Zarządzanie wieloma instancjami                         ║"
    echo "║  🔄 Automatyczne restartowanie                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Funkcja sprawdzająca czy screen jest zainstalowany
check_screen() {
    if ! command -v screen &> /dev/null; then
        echo -e "${RED}❌ Screen nie jest zainstalowany!${NC}"
        echo -e "${YELLOW}Zainstaluj screen używając:${NC}"
        echo "  Ubuntu/Debian: sudo apt install screen"
        echo "  CentOS/RHEL: sudo yum install screen"
        exit 1
    fi
}

# Funkcja sprawdzająca czy Python jest zainstalowany
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 nie jest zainstalowany!${NC}"
        exit 1
    fi
    
    # Sprawdź czy pip jest dostępny
    if ! command -v pip3 &> /dev/null; then
        echo -e "${RED}❌ pip3 nie jest zainstalowany!${NC}"
        exit 1
    fi
}

# Funkcja instalująca zależności
install_dependencies() {
    echo -e "${CYAN}📦 Sprawdzam zależności Python...${NC}"

    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ Plik requirements.txt nie istnieje!${NC}"
        exit 1
    fi

    # Sprawdź czy wszystkie pakiety są zainstalowane
    # Użyj --break-system-packages dla systemów z PEP 668
    pip3 install -r requirements.txt --break-system-packages --quiet 2>/dev/null || \
    pip3 install -r requirements.txt --user --quiet 2>/dev/null || \
    {
        echo -e "${YELLOW}⚠️  Nie można zainstalować przez pip. Sprawdzam pakiety systemowe...${NC}"

        # Sprawdź czy pakiety są dostępne jako pakiety systemowe
        missing_packages=""

        # Sprawdź requests
        python3 -c "import requests" 2>/dev/null || missing_packages="$missing_packages python3-requests"

        # Sprawdź colorama
        python3 -c "import colorama" 2>/dev/null || missing_packages="$missing_packages python3-colorama"

        # Sprawdź beautifulsoup4
        python3 -c "import bs4" 2>/dev/null || missing_packages="$missing_packages python3-bs4"

        if [ -n "$missing_packages" ]; then
            echo -e "${YELLOW}Instaluję brakujące pakiety systemowe:$missing_packages${NC}"
            apt update && apt install -y $missing_packages
        fi
    }

    # Sprawdź czy wszystkie moduły są dostępne
    python3 -c "import requests, colorama, json, os, sys" 2>/dev/null

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Wszystkie zależności są dostępne${NC}"
    else
        echo -e "${RED}❌ Niektóre zależności nadal niedostępne!${NC}"
        echo -e "${YELLOW}Kontynuuję mimo to...${NC}"
    fi
}

# Funkcja wyświetlająca aktywne sesje screen
show_active_sessions() {
    echo -e "${BLUE}📋 Aktywne sesje generatora:${NC}"
    
    sessions=$(screen -ls | grep "generator_" | wc -l)
    
    if [ $sessions -eq 0 ]; then
        echo -e "${YELLOW}   Brak aktywnych sesji${NC}"
    else
        screen -ls | grep "generator_" | while read line; do
            session_name=$(echo $line | cut -d'.' -f2 | cut -d$'\t' -f1)
            status=$(echo $line | grep -o '([^)]*)')
            echo -e "   🟢 $session_name $status"
        done
    fi
    echo
}

# Funkcja uruchamiająca nową sesję
start_new_session() {
    echo -e "${CYAN}🚀 Uruchamianie nowej sesji generatora${NC}"
    
    # Pobierz nazwę sesji od użytkownika
    read -p "Podaj nazwę sesji (np. 'ciekawostki', 'porady'): " session_name
    
    if [ -z "$session_name" ]; then
        echo -e "${RED}❌ Nazwa sesji nie może być pusta!${NC}"
        return 1
    fi
    
    # Sprawdź czy sesja już istnieje
    if screen -ls | grep -q "generator_$session_name"; then
        echo -e "${RED}❌ Sesja 'generator_$session_name' już istnieje!${NC}"
        return 1
    fi
    
    # Uruchom nową sesję screen
    echo -e "${GREEN}✅ Uruchamiam sesję 'generator_$session_name'...${NC}"
    screen -dmS "generator_$session_name" bash -c "cd $(pwd) && python3 generator.py; exec bash"
    
    sleep 2
    
    # Sprawdź czy sesja została utworzona
    if screen -ls | grep -q "generator_$session_name"; then
        echo -e "${GREEN}✅ Sesja utworzona pomyślnie!${NC}"
        echo -e "${YELLOW}💡 Aby dołączyć do sesji użyj: screen -r generator_$session_name${NC}"
        echo -e "${YELLOW}💡 Aby odłączyć się od sesji: Ctrl+A, potem D${NC}"
    else
        echo -e "${RED}❌ Błąd tworzenia sesji!${NC}"
    fi
}

# Funkcja dołączania do istniejącej sesji
attach_to_session() {
    echo -e "${CYAN}🔗 Dołączanie do istniejącej sesji${NC}"
    
    # Pokaż dostępne sesje
    sessions=$(screen -ls | grep "generator_" | cut -d'.' -f2 | cut -d$'\t' -f1)
    
    if [ -z "$sessions" ]; then
        echo -e "${YELLOW}⚠️  Brak aktywnych sesji generatora${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Dostępne sesje:${NC}"
    echo "$sessions" | nl -w2 -s'. '
    
    read -p "Wybierz numer sesji: " choice
    
    session=$(echo "$sessions" | sed -n "${choice}p")
    
    if [ -z "$session" ]; then
        echo -e "${RED}❌ Nieprawidłowy wybór!${NC}"
        return 1
    fi
    
    echo -e "${GREEN}🔗 Dołączam do sesji '$session'...${NC}"
    screen -r "$session"
}

# Funkcja zatrzymywania sesji
stop_session() {
    echo -e "${CYAN}🛑 Zatrzymywanie sesji generatora${NC}"
    
    # Pokaż dostępne sesje
    sessions=$(screen -ls | grep "generator_" | cut -d'.' -f2 | cut -d$'\t' -f1)
    
    if [ -z "$sessions" ]; then
        echo -e "${YELLOW}⚠️  Brak aktywnych sesji generatora${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Dostępne sesje:${NC}"
    echo "$sessions" | nl -w2 -s'. '
    echo "$(echo "$sessions" | wc -l | awk '{print $1+1}'). Wszystkie sesje"
    
    read -p "Wybierz numer sesji do zatrzymania: " choice
    
    total_sessions=$(echo "$sessions" | wc -l)
    
    if [ "$choice" -eq $((total_sessions + 1)) ]; then
        # Zatrzymaj wszystkie sesje
        echo -e "${YELLOW}⚠️  Zatrzymuję wszystkie sesje generatora...${NC}"
        echo "$sessions" | while read session; do
            screen -S "$session" -X quit
            echo -e "   🛑 Zatrzymano: $session"
        done
    else
        session=$(echo "$sessions" | sed -n "${choice}p")
        
        if [ -z "$session" ]; then
            echo -e "${RED}❌ Nieprawidłowy wybór!${NC}"
            return 1
        fi
        
        echo -e "${YELLOW}🛑 Zatrzymuję sesję '$session'...${NC}"
        screen -S "$session" -X quit
        echo -e "${GREEN}✅ Sesja zatrzymana${NC}"
    fi
}

# Funkcja wyświetlająca menu główne
show_menu() {
    echo -e "${BLUE}📋 MENU GŁÓWNE:${NC}"
    echo "1. 🚀 Uruchom nową sesję generatora"
    echo "2. 🔗 Dołącz do istniejącej sesji"
    echo "3. 📊 Pokaż aktywne sesje"
    echo "4. 🛑 Zatrzymaj sesję"
    echo "5. 🔄 Odśwież widok"
    echo "6. ❌ Wyjście"
    echo
}

# Główna funkcja
main() {
    show_header
    
    # Sprawdź wymagania systemowe
    check_screen
    check_python
    install_dependencies
    
    while true; do
        show_active_sessions
        show_menu
        
        read -p "Wybierz opcję (1-6): " choice
        
        case $choice in
            1)
                start_new_session
                ;;
            2)
                attach_to_session
                ;;
            3)
                echo -e "${CYAN}🔄 Odświeżam listę sesji...${NC}"
                sleep 1
                ;;
            4)
                stop_session
                ;;
            5)
                show_header
                ;;
            6)
                echo -e "${GREEN}👋 Do widzenia!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Nieprawidłowy wybór! Wybierz 1-6.${NC}"
                sleep 2
                ;;
        esac
        
        echo
        read -p "Naciśnij Enter aby kontynuować..."
        show_header
    done
}

# Uruchom główną funkcję
main
