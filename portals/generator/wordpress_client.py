#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from colorama import Fore, Style
import re
from bs4 import BeautifulSoup

class WordPressClient:
    """Klient do komunikacji z WordPress API"""
    
    def __init__(self, site_url: str, username: str, password: str):
        self.site_url = site_url.rstrip('/')
        self.username = username
        self.password = password
        self.api_base = f"{self.site_url}/wp-json/wp/v2"
        
        # Przygotuj autoryzację
        credentials = f"{username}:{password}"
        token = base64.b64encode(credentials.encode()).decode()
        self.headers = {
            "Authorization": f"Basic {token}",
            "Content-Type": "application/json"
        }
    
    def test_connection(self) -> bool:
        """Testuje połączenie z WordPress API"""
        try:
            response = requests.get(
                f"{self.api_base}/categories",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"{Fore.GREEN}✓ Połączenie z WordPress działa poprawnie{Style.RESET_ALL}")
                return True
            elif response.status_code == 401:
                print(f"{Fore.RED}✗ Błąd autoryzacji - sprawdź dane logowania{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}✗ Błąd połączenia: {response.status_code}{Style.RESET_ALL}")
                return False
                
        except requests.RequestException as e:
            print(f"{Fore.RED}✗ Błąd połączenia: {e}{Style.RESET_ALL}")
            return False
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """Pobiera listę kategorii WordPress"""
        try:
            response = requests.get(
                f"{self.api_base}/categories",
                headers=self.headers,
                params={"per_page": 100},
                timeout=30
            )
            
            if response.status_code == 200:
                categories = response.json()
                print(f"{Fore.GREEN}✓ Pobrano {len(categories)} kategorii{Style.RESET_ALL}")
                return categories
            else:
                print(f"{Fore.RED}✗ Błąd pobierania kategorii: {response.status_code}{Style.RESET_ALL}")
                return []
                
        except requests.RequestException as e:
            print(f"{Fore.RED}✗ Błąd pobierania kategorii: {e}{Style.RESET_ALL}")
            return []
    
    def clean_html_for_wordpress(self, html_content: str) -> str:
        """Czyści HTML dla WordPress - usuwa niepotrzebne atrybuty"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Usuń atrybuty style, class, id
        for tag in soup.find_all():
            if tag.has_attr('style'):
                del tag['style']
            if tag.has_attr('class'):
                del tag['class']
            if tag.has_attr('id'):
                del tag['id']
        
        # Zwróć czysty HTML
        return str(soup)
    
    def extract_title_and_content(self, html_file_path: str) -> tuple[str, str]:
        """Wyciąga tytuł i treść z pliku HTML"""
        try:
            with open(html_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Znajdź tytuł (H1)
            title_tag = soup.find('h1')
            title = title_tag.get_text().strip() if title_tag else "Bez tytułu"
            
            # Usuń H1 z treści (WordPress doda własny tytuł)
            if title_tag:
                title_tag.decompose()
            
            # Wyciągnij treść z body lub całą treść
            body = soup.find('body')
            if body:
                # Usuń niepotrzebne elementy
                for element in body.find_all(['script', 'style', 'meta', 'link']):
                    element.decompose()
                
                content_html = str(body)
                # Usuń tagi body
                content_html = re.sub(r'</?body[^>]*>', '', content_html)
            else:
                # Jeśli nie ma body, weź całą treść
                content_html = str(soup)
            
            # Wyczyść HTML
            content_html = self.clean_html_for_wordpress(content_html)
            
            return title, content_html.strip()
            
        except Exception as e:
            print(f"{Fore.RED}✗ Błąd odczytu pliku {html_file_path}: {e}{Style.RESET_ALL}")
            return "Błąd odczytu", ""
    
    def create_post(self, title: str, content: str, category_id: int, 
                   status: str = "draft", publish_date: Optional[datetime] = None) -> Optional[Dict]:
        """Tworzy nowy post w WordPress"""
        
        post_data = {
            "title": title,
            "content": content,
            "status": status,
            "categories": [category_id]
        }
        
        # Dodaj datę publikacji jeśli podana
        if publish_date:
            post_data["date"] = publish_date.isoformat()
        
        try:
            response = requests.post(
                f"{self.api_base}/posts",
                headers=self.headers,
                json=post_data,
                timeout=60
            )
            
            if response.status_code == 201:
                post = response.json()
                print(f"{Fore.GREEN}✓ Utworzono post: {title}{Style.RESET_ALL}")
                return post
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get("message", f"HTTP {response.status_code}")
                print(f"{Fore.RED}✗ Błąd tworzenia posta '{title}': {error_msg}{Style.RESET_ALL}")
                return None
                
        except requests.RequestException as e:
            print(f"{Fore.RED}✗ Błąd połączenia przy tworzeniu posta '{title}': {e}{Style.RESET_ALL}")
            return None
        except json.JSONDecodeError as e:
            print(f"{Fore.RED}✗ Błąd parsowania odpowiedzi dla posta '{title}': {e}{Style.RESET_ALL}")
            return None
    
    def schedule_posts_from_files(self, html_files: List[str], category_id: int, 
                                 days_interval: int = 1, start_date: Optional[datetime] = None) -> Dict[str, int]:
        """Planuje publikację postów z plików HTML"""
        
        if start_date is None:
            start_date = datetime.now()
        
        results = {"success": 0, "failed": 0, "total": len(html_files)}
        current_date = start_date
        
        print(f"{Fore.CYAN}📅 Planowanie publikacji {len(html_files)} artykułów{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📅 Rozpoczęcie: {start_date.strftime('%Y-%m-%d %H:%M')}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📅 Interwał: co {days_interval} dni{Style.RESET_ALL}")
        
        for i, html_file in enumerate(html_files, 1):
            print(f"\n{Fore.BLUE}📝 Przetwarzanie {i}/{len(html_files)}: {html_file}{Style.RESET_ALL}")
            
            # Wyciągnij tytuł i treść
            title, content = self.extract_title_and_content(html_file)
            
            if not content.strip():
                print(f"{Fore.YELLOW}⚠ Pominięto - brak treści{Style.RESET_ALL}")
                results["failed"] += 1
                continue
            
            # Określ status i datę
            if days_interval == 0:
                # Natychmiastowa publikacja
                status = "publish"
                publish_date = None
                print(f"{Fore.GREEN}🚀 Publikacja natychmiastowa{Style.RESET_ALL}")
            else:
                # Zaplanowana publikacja
                status = "future"
                publish_date = current_date
                print(f"{Fore.YELLOW}⏰ Zaplanowano na: {publish_date.strftime('%Y-%m-%d %H:%M')}{Style.RESET_ALL}")
            
            # Utwórz post
            post = self.create_post(title, content, category_id, status, publish_date)
            
            if post:
                results["success"] += 1
                if publish_date:
                    print(f"{Fore.GREEN}✓ Post zaplanowany (ID: {post.get('id')}){Style.RESET_ALL}")
                else:
                    print(f"{Fore.GREEN}✓ Post opublikowany (ID: {post.get('id')}){Style.RESET_ALL}")
            else:
                results["failed"] += 1
            
            # Przygotuj datę dla następnego posta
            if days_interval > 0:
                current_date += timedelta(days=days_interval)
        
        return results
    
    def get_posts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Pobiera listę ostatnich postów"""
        try:
            response = requests.get(
                f"{self.api_base}/posts",
                headers=self.headers,
                params={"per_page": limit, "status": "any"},
                timeout=30
            )
            
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print(f"{Fore.RED}✗ Błąd pobierania postów: {response.status_code}{Style.RESET_ALL}")
                return []
                
        except requests.RequestException as e:
            print(f"{Fore.RED}✗ Błąd pobierania postów: {e}{Style.RESET_ALL}")
            return []
