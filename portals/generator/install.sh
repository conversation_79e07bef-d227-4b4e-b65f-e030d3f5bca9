#!/bin/bash

# Skrypt instalacyjny dla Generatora Artykułów AI
# Autor: AI Assistant

# Kolory
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funkcja wyświetlająca nagłówek
show_header() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 INSTALATOR GENERATORA AI                     ║"
    echo "║                                                              ║"
    echo "║  🚀 Automatyczna instalacja i konfiguracja                  ║"
    echo "║  📦 Instalacja zależności                                   ║"
    echo "║  ⚙️  Konfiguracja uprawnień                                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Funkcja sprawdzająca system operacyjny
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
            echo -e "${GREEN}✅ Wykryto system: Debian/Ubuntu${NC}"
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
            echo -e "${GREEN}✅ Wykryto system: RedHat/CentOS${NC}"
        else
            OS="linux"
            echo -e "${GREEN}✅ Wykryto system: Linux (ogólny)${NC}"
        fi
    else
        OS="unknown"
        echo -e "${YELLOW}⚠️  Nieznany system operacyjny${NC}"
    fi
}

# Funkcja instalująca screen
install_screen() {
    echo -e "${CYAN}📦 Instalowanie screen...${NC}"
    
    if command -v screen &> /dev/null; then
        echo -e "${GREEN}✅ Screen już zainstalowany${NC}"
        return 0
    fi
    
    case $OS in
        "debian")
            sudo apt update
            sudo apt install -y screen
            ;;
        "redhat")
            sudo yum install -y screen
            ;;
        *)
            echo -e "${RED}❌ Nie można automatycznie zainstalować screen${NC}"
            echo -e "${YELLOW}Zainstaluj screen ręcznie dla swojego systemu${NC}"
            return 1
            ;;
    esac
    
    if command -v screen &> /dev/null; then
        echo -e "${GREEN}✅ Screen zainstalowany pomyślnie${NC}"
        return 0
    else
        echo -e "${RED}❌ Błąd instalacji screen${NC}"
        return 1
    fi
}

# Funkcja sprawdzająca Python
check_python() {
    echo -e "${CYAN}🐍 Sprawdzanie Python...${NC}"
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        echo -e "${GREEN}✅ Python 3 zainstalowany: $PYTHON_VERSION${NC}"
    else
        echo -e "${RED}❌ Python 3 nie jest zainstalowany!${NC}"
        echo -e "${YELLOW}Zainstaluj Python 3.6+ przed kontynuowaniem${NC}"
        return 1
    fi
    
    if command -v pip3 &> /dev/null; then
        PIP_VERSION=$(pip3 --version | cut -d' ' -f2)
        echo -e "${GREEN}✅ pip3 zainstalowany: $PIP_VERSION${NC}"
    else
        echo -e "${RED}❌ pip3 nie jest zainstalowany!${NC}"
        echo -e "${YELLOW}Zainstaluj pip3 przed kontynuowaniem${NC}"
        return 1
    fi
    
    return 0
}

# Funkcja instalująca zależności Python
install_python_deps() {
    echo -e "${CYAN}📦 Instalowanie zależności Python...${NC}"

    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ Plik requirements.txt nie istnieje!${NC}"
        return 1
    fi

    echo -e "${BLUE}Próbuję zainstalować pakiety z requirements.txt...${NC}"

    # Spróbuj różne metody instalacji
    if pip3 install -r requirements.txt --break-system-packages --quiet 2>/dev/null; then
        echo -e "${GREEN}✅ Zależności zainstalowane przez pip (--break-system-packages)${NC}"
        return 0
    elif pip3 install -r requirements.txt --user --quiet 2>/dev/null; then
        echo -e "${GREEN}✅ Zależności zainstalowane przez pip (--user)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Pip nie działa. Instaluję pakiety systemowe...${NC}"

        # Zainstaluj pakiety systemowe
        case $OS in
            "debian")
                apt update
                apt install -y python3-requests python3-colorama python3-bs4 python3-dotenv
                ;;
            "redhat")
                yum install -y python3-requests python3-colorama python3-beautifulsoup4
                ;;
            *)
                echo -e "${YELLOW}⚠️  Nieznany system - sprawdź ręcznie pakiety Python${NC}"
                ;;
        esac

        # Sprawdź czy moduły są dostępne
        python3 -c "import requests, colorama" 2>/dev/null

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Podstawowe zależności dostępne${NC}"
            return 0
        else
            echo -e "${RED}❌ Nie można zainstalować zależności${NC}"
            return 1
        fi
    fi
}

# Funkcja ustawiająca uprawnienia
set_permissions() {
    echo -e "${CYAN}🔐 Ustawianie uprawnień plików...${NC}"
    
    # Lista plików wykonywalnych
    executable_files=(
        "generator.py"
        "run_generator.sh"
        "test_setup.py"
        "install.sh"
    )
    
    for file in "${executable_files[@]}"; do
        if [ -f "$file" ]; then
            chmod +x "$file"
            echo -e "${GREEN}✅ $file - uprawnienia ustawione${NC}"
        else
            echo -e "${YELLOW}⚠️  $file - plik nie istnieje${NC}"
        fi
    done
    
    return 0
}

# Funkcja tworząca przykładową konfigurację
create_sample_config() {
    echo -e "${CYAN}⚙️  Tworzenie przykładowej konfiguracji...${NC}"
    
    if [ -f "generator_config.json" ]; then
        echo -e "${YELLOW}⚠️  Plik konfiguracyjny już istnieje${NC}"
        read -p "Czy zastąpić istniejącą konfigurację? (tak/nie): " replace
        if [[ $replace != "tak" && $replace != "t" ]]; then
            echo -e "${BLUE}ℹ️  Zachowuję istniejącą konfigurację${NC}"
            return 0
        fi
    fi
    
    if [ -f "generator_config.json.example" ]; then
        cp "generator_config.json.example" "generator_config.json"
        echo -e "${GREEN}✅ Utworzono plik konfiguracyjny z przykładu${NC}"
        echo -e "${YELLOW}⚠️  Pamiętaj o ustawieniu klucza API OpenRouter!${NC}"
    else
        echo -e "${YELLOW}⚠️  Brak pliku przykładowej konfiguracji${NC}"
    fi
    
    return 0
}

# Funkcja tworząca folder wyjściowy
create_output_folder() {
    echo -e "${CYAN}📁 Tworzenie folderu wyjściowego...${NC}"
    
    output_folder="artykuly"
    
    if [ ! -d "$output_folder" ]; then
        mkdir -p "$output_folder"
        echo -e "${GREEN}✅ Utworzono folder: $output_folder${NC}"
    else
        echo -e "${BLUE}ℹ️  Folder już istnieje: $output_folder${NC}"
    fi
    
    return 0
}

# Funkcja uruchamiająca testy
run_tests() {
    echo -e "${CYAN}🧪 Uruchamianie testów konfiguracji...${NC}"
    
    if [ -f "test_setup.py" ]; then
        python3 test_setup.py
        return $?
    else
        echo -e "${YELLOW}⚠️  Plik testowy nie istnieje${NC}"
        return 1
    fi
}

# Funkcja wyświetlająca instrukcje końcowe
show_final_instructions() {
    echo -e "\n${GREEN}🎉 INSTALACJA ZAKOŃCZONA POMYŚLNIE!${NC}"
    echo -e "${CYAN}{'='*60}${NC}"
    echo -e "${BLUE}📋 NASTĘPNE KROKI:${NC}"
    echo
    echo -e "${YELLOW}1. Ustaw klucz API OpenRouter:${NC}"
    echo "   Edytuj plik: generator_config.json"
    echo "   Wstaw swój klucz API w pole 'openrouter_api_key'"
    echo
    echo -e "${YELLOW}2. Uruchom generator:${NC}"
    echo "   ./run_generator.sh    (menedżer sesji)"
    echo "   python3 generator.py  (bezpośrednio)"
    echo
    echo -e "${YELLOW}3. Praca z screen:${NC}"
    echo "   screen -S generator_nazwa  (nowa sesja)"
    echo "   screen -r generator_nazwa  (dołącz do sesji)"
    echo "   screen -ls                 (lista sesji)"
    echo
    echo -e "${YELLOW}4. Monitorowanie:${NC}"
    echo "   tail -f generator.log      (logi w czasie rzeczywistym)"
    echo
    echo -e "${CYAN}{'='*60}${NC}"
    echo -e "${GREEN}✨ Powodzenia w generowaniu artykułów!${NC}"
}

# Główna funkcja instalacyjna
main() {
    show_header
    
    echo -e "${BLUE}🚀 Rozpoczynam instalację...${NC}\n"
    
    # Wykryj system operacyjny
    detect_os
    echo
    
    # Sprawdź Python
    if ! check_python; then
        echo -e "${RED}❌ Instalacja przerwana - brak Python${NC}"
        exit 1
    fi
    echo
    
    # Zainstaluj screen
    if ! install_screen; then
        echo -e "${YELLOW}⚠️  Kontynuuję bez screen (ograniczona funkcjonalność)${NC}"
    fi
    echo
    
    # Zainstaluj zależności Python
    if ! install_python_deps; then
        echo -e "${RED}❌ Instalacja przerwana - błąd zależności${NC}"
        exit 1
    fi
    echo
    
    # Ustaw uprawnienia
    set_permissions
    echo
    
    # Utwórz konfigurację
    create_sample_config
    echo
    
    # Utwórz folder wyjściowy
    create_output_folder
    echo
    
    # Uruchom testy
    echo -e "${BLUE}🧪 Testowanie instalacji...${NC}"
    if run_tests; then
        echo -e "${GREEN}✅ Wszystkie testy przeszły pomyślnie${NC}"
    else
        echo -e "${YELLOW}⚠️  Niektóre testy nie przeszły - sprawdź konfigurację${NC}"
    fi
    
    # Instrukcje końcowe
    show_final_instructions
}

# Sprawdź czy skrypt jest uruchamiany jako root
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Uruchamiasz skrypt jako root${NC}"
    echo -e "${YELLOW}Niektóre operacje mogą wymagać uprawnień użytkownika${NC}"
    read -p "Kontynuować? (tak/nie): " continue_as_root
    if [[ $continue_as_root != "tak" && $continue_as_root != "t" ]]; then
        echo -e "${BLUE}Uruchom skrypt jako zwykły użytkownik${NC}"
        exit 0
    fi
fi

# Uruchom główną funkcję
main
