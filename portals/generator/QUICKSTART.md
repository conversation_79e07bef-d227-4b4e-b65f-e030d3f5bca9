# ⚡ <PERSON><PERSON><PERSON>ki Start - Generator Artykułów AI

## 🚀 W 5 minut do pierwszego artykułu!

### Krok 1: Sprawd<PERSON> instalację
```bash
cd /www/portals/generator
python3 test_setup.py
```

### Krok 2: Ustaw klucz API OpenRouter
```bash
# Skopiuj przykładową konfigurację
cp generator_config.json.example generator_config.json

# Edytuj i wstaw swój klucz API
nano generator_config.json
```

### Krok 3: Uruchom generator
```bash
# Opcja A: Menedżer sesji (zalecane)
./run_generator.sh

# Opcja B: Szybki start z tematami
./quick_start.sh

# Opcja C: Bezpośrednio
python3 generator.py
```

## 📝 Przykład użycia

### 1. Uruchom generator:
```bash
./run_generator.sh
```

### 2. <PERSON><PERSON><PERSON><PERSON> "Uruchom nową sesję generatora"

### 3. Podaj nazwę sesji (np. "ciekawostki")

### 4. W generatorze:
- Wprowadź klucz API OpenRouter
- Wybierz model (np. GPT-3.5 Turbo)
- Ustaw folder wyjściowy (np. "ciekawostki")
- Wybierz długość: Długi (1200+ słów)
- Dodaj tytuły artykułów:
  ```
  10 najlepszych sposobów na szybkie sprzątanie domu
  Jak wybrać firmę sprzątającą - kompletny przewodnik
  Ekologiczne środki czyszczące - czy warto?
  ```

### 5. Uruchom generowanie i obserwuj postęp!

## 🎯 Gotowe tematy (Quick Start)

```bash
./quick_start.sh
```

Dostępne tematy:
- **biura** - sprzątanie biur i powierzchni biurowych
- **domy** - sprzątanie domów i mieszkań
- **dywany** - czyszczenie dywanów i wykładzin
- **okna** - mycie okien i szyb
- **po-remoncie** - sprzątanie po remontach
- **ekologiczne** - naturalne środki czyszczące
- **przemysłowe** - sprzątanie hal i zakładów
- **medyczne** - sprzątanie placówek medycznych
- **hotele** - sprzątanie obiektów noclegowych
- **sklepy** - sprzątanie powierzchni handlowych

## 🔧 Podstawowe komendy

### Zarządzanie sesjami screen:
```bash
# Lista sesji
screen -ls

# Dołącz do sesji
screen -r generator_nazwa

# Odłącz się od sesji
# W sesji: Ctrl+A, potem D

# Zatrzymaj sesję
screen -S generator_nazwa -X quit
```

### Monitorowanie:
```bash
# Logi w czasie rzeczywistym
tail -f generator.log

# Status sesji
./run_generator.sh  # opcja 3

# Monitor live
./quick_start.sh    # opcja 3
```

## 📊 Przykład 50 instancji

### Automatyczne uruchomienie:
```bash
./quick_start.sh
# Wybierz "all" - uruchomi wszystkie dostępne tematy
```

### Ręczne uruchomienie wielu sesji:
```bash
# Skrypt do uruchomienia 50 sesji
for i in {1..50}; do
    screen -dmS "generator_$i" bash -c "cd $(pwd) && python3 generator.py"
    sleep 2
done
```

### Sprawdzenie wszystkich sesji:
```bash
screen -ls | grep generator_
```

## 🎨 Przykład wygenerowanego artykułu

Po uruchomieniu otrzymasz pliki HTML w folderze wyjściowym:

**Nazwa pliku:** `jak-wybrac-firme-sprzatajaca_20240115_143022.html`

**Zawartość:**
```html
<!-- Meta description: Dowiedz się jak wybrać najlepszą firmę sprzątającą. Praktyczne porady, na co zwrócić uwagę i jak uniknąć błędów przy wyborze. -->

<h1>Jak wybrać firmę sprzątającą - kompletny przewodnik</h1>

<p>Wybór odpowiedniej firmy sprzątającej to decyzja, która może znacząco wpłynąć na komfort życia lub efektywność pracy w biurze...</p>

<h2>Na co zwrócić uwagę przy wyborze firmy sprzątającej</h2>
<p>Pierwszym krokiem w wyborze firmy sprzątającej jest...</p>

<ul>
<li>Doświadczenie i referencje</li>
<li>Zakres oferowanych usług</li>
<li>Używane środki czyszczące</li>
<li>Ubezpieczenie i gwarancje</li>
</ul>

<!-- Dalszy ciąg artykułu... -->
```

## ⚠️ Najczęstsze problemy

### Problem: "Błąd API"
**Rozwiązanie:** Sprawdź klucz API w `generator_config.json`

### Problem: "Brak uprawnień"
**Rozwiązanie:** `chmod +x *.sh *.py`

### Problem: "Screen nie działa"
**Rozwiązanie:** `sudo apt install screen`

### Problem: "Artykuł za krótki"
**Rozwiązanie:** Generator automatycznie rozszerza artykuły

## 🎯 Wskazówki pro

1. **Używaj konkretnych słów kluczowych** - lepsze SEO
2. **Ustaw opóźnienia 30-60s** - unikaj limitów API
3. **Monitoruj logi** - `tail -f generator.log`
4. **Regularnie rób backup** - `tar -czf backup.tar.gz artykuly/`
5. **Używaj opisowych nazw sesji** - łatwiejsze zarządzanie

## 📞 Pomoc

- **Testy:** `python3 test_setup.py`
- **Logi:** `tail -f generator.log`
- **Dokumentacja:** `README.md`
- **Wdrożenie:** `DEPLOYMENT.md`

---

**Gotowy do generowania tysięcy artykułów? Zaczynaj!** 🚀
