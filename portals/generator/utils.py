#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import sys
from datetime import datetime
from typing import List, Optional
from colorama import Fore, Style, init

# Inicjalizuj colorama
init(autoreset=True)

class Logger:
    """Klasa do logowania z kolorami i zapisem do pliku"""
    
    def __init__(self, log_file: str = "generator.log"):
        self.log_file = log_file
        self.session_start = datetime.now()
    
    def log(self, message: str, level: str = "INFO", color: str = Fore.WHITE):
        """Loguje wiadomość z timestampem"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        # Wyświetl w konsoli z kolorem
        print(f"{color}{log_entry}{Style.RESET_ALL}")
        
        # Zapisz do pliku
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except IOError:
            pass  # Ignoruj błędy zapisu do loga
    
    def info(self, message: str):
        self.log(message, "INFO", Fore.WHITE)
    
    def success(self, message: str):
        self.log(message, "SUCCESS", Fore.GREEN)
    
    def warning(self, message: str):
        self.log(message, "WARNING", Fore.YELLOW)
    
    def error(self, message: str):
        self.log(message, "ERROR", Fore.RED)
    
    def debug(self, message: str):
        self.log(message, "DEBUG", Fore.CYAN)

def clear_screen():
    """Czyści ekran konsoli"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Wyświetla nagłówek aplikacji"""
    header = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    GENERATOR ARTYKUŁÓW AI                    ║
║                     OpenRouter Edition                       ║
║                                                              ║
║  🤖 Automatyczne generowanie artykułów SEO                  ║
║  📝 Minimum 1200 słów na artykuł                           ║
║  🎯 Optymalizacja pod kątem SEO                             ║
║  💾 Automatyczny zapis do HTML                              ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(header)

def get_user_input(prompt: str, required: bool = True, input_type: type = str) -> Optional[any]:
    """Pobiera input od użytkownika z walidacją"""
    while True:
        try:
            user_input = input(f"{Fore.YELLOW}{prompt}{Style.RESET_ALL}")
            
            if not user_input.strip() and required:
                print(f"{Fore.RED}To pole jest wymagane!{Style.RESET_ALL}")
                continue
            
            if not user_input.strip() and not required:
                return None
            
            if input_type == int:
                return int(user_input)
            elif input_type == float:
                return float(user_input)
            else:
                return user_input.strip()
                
        except ValueError:
            print(f"{Fore.RED}Nieprawidłowy format! Spróbuj ponownie.{Style.RESET_ALL}")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Przerwano przez użytkownika{Style.RESET_ALL}")
            sys.exit(0)

def get_titles_from_user() -> List[str]:
    """Pobiera tytuły artykułów od użytkownika"""
    titles = []
    
    print(f"\n{Fore.GREEN}📝 DODAWANIE TYTUŁÓW ARTYKUŁÓW{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Możesz dodawać tytuły na kilka sposobów:{Style.RESET_ALL}")
    print("1. Po jednym tytule (naciśnij Enter po każdym)")
    print("2. Wklej wszystkie tytuły naraz (oddzielone średnikami ;)")
    print("3. Wpisz 'koniec' aby zakończyć dodawanie")
    print()
    
    while True:
        title = get_user_input("Tytuł artykułu (lub 'koniec'): ", required=False)
        
        if not title or title.lower() == 'koniec':
            break
        
        # Sprawdź czy to może być lista tytułów (średniki lub entery)
        if ';' in title or '\n' in title:
            # Preferuj średniki, ale obsługuj też entery
            if ';' in title:
                new_titles = [t.strip() for t in title.split(';') if t.strip()]
            else:
                new_titles = [t.strip() for t in title.split('\n') if t.strip()]

            titles.extend(new_titles)
            print(f"{Fore.GREEN}✓ Dodano {len(new_titles)} tytułów{Style.RESET_ALL}")
        else:
            titles.append(title)
            print(f"{Fore.GREEN}✓ Dodano: {title[:50]}...{Style.RESET_ALL}")
    
    return titles

def validate_api_key(api_key: str) -> bool:
    """Waliduje format klucza API OpenRouter"""
    if not api_key:
        return False
    
    # Klucz OpenRouter powinien zaczynać się od "sk-or-"
    return api_key.strip().startswith('sk-or-') and len(api_key.strip()) > 20

def format_time_duration(seconds: int) -> str:
    """Formatuje czas w sekundach na czytelny format"""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{minutes}min {secs}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}h {minutes}min"

def create_folder_if_not_exists(folder_path: str) -> bool:
    """Tworzy folder jeśli nie istnieje"""
    try:
        os.makedirs(folder_path, exist_ok=True)
        return True
    except OSError as e:
        print(f"{Fore.RED}Błąd tworzenia folderu {folder_path}: {e}{Style.RESET_ALL}")
        return False

def show_summary(generated: int, failed: int, total_time: int, output_folder: str):
    """Wyświetla podsumowanie generowania"""
    print(f"\n{Fore.GREEN}{'='*60}")
    print(f"                    PODSUMOWANIE GENEROWANIA")
    print(f"{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}📊 Statystyki:{Style.RESET_ALL}")
    print(f"   ✅ Wygenerowane artykuły: {Fore.GREEN}{generated}{Style.RESET_ALL}")
    print(f"   ❌ Nieudane próby: {Fore.RED}{failed}{Style.RESET_ALL}")
    print(f"   ⏱️  Całkowity czas: {Fore.YELLOW}{format_time_duration(total_time)}{Style.RESET_ALL}")
    print(f"   📁 Folder wyjściowy: {Fore.BLUE}{output_folder}{Style.RESET_ALL}")
    
    if generated > 0:
        avg_time = total_time / generated
        print(f"   ⚡ Średni czas na artykuł: {Fore.MAGENTA}{format_time_duration(int(avg_time))}{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")

def confirm_action(message: str) -> bool:
    """Prosi użytkownika o potwierdzenie akcji"""
    while True:
        response = get_user_input(f"{message} (tak/nie): ", required=True).lower()
        if response in ['tak', 't', 'yes', 'y']:
            return True
        elif response in ['nie', 'n', 'no']:
            return False
        else:
            print(f"{Fore.RED}Odpowiedz 'tak' lub 'nie'{Style.RESET_ALL}")
