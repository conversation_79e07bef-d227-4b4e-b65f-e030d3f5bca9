<!DOCTYPE html>
<html lang="pl" id="htmlRoot">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlogScribe AI - Generator AI artykułów na bloga</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        h1 span {
            color: #6366f1;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
        }
        .btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
        }
        .btn:hover {
            background: #4f46e5;
        }
        .btn:disabled {
            background: #a5a6f6;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        .article {
            background: #f9f9f9;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .article-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }
        #titlesList {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        .title-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px;
            background: white;
            border-radius: 3px;
        }
        .remove-btn {
            background: #ff4d4d;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        /* Style dla wskaźników ładowania i błędów */
        .loading-container {
            text-align: center;
            padding: 20px;
            background: #f0f4ff;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .progress-container {
            margin: 15px 0;
        }
        
        .progress-bar {
            height: 10px;
            background: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: #6366f1;
            border-radius: 5px;
            transition: width 0.5s ease;
            width: 0%;
        }
        
        .error-container {
            background: #ffebee;
            color: #d32f2f;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 5px solid #d32f2f;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-pending {
            background-color: #ffb74d;
        }
        
        .status-generating {
            background-color: #64b5f6;
            animation: pulse 1.5s infinite;
        }
        
        .status-complete {
            background-color: #66bb6a;
        }
        
        .status-error {
            background-color: #e53935;
        }
        
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
        
        .status-text {
            font-size: 14px;
        }
        
        .time-remaining {
            font-style: italic;
            margin-top: 10px;
            font-size: 13px;
            color: #666;
        }
        
        /* Style dla integracji z WordPress */
        .wordpress-integration {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            border: 1px solid #ddd;
        }
        
        .wordpress-integration h2 {
            color: #6366f1;
            margin-top: 0;
        }
        
        .schedule-options {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-top: 10px;
        }
        
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            background: white;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        /* Style dla pola postępu generowania */
        .generation-progress {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #cce5ff;
            max-height: 60px;
            overflow-y: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h1>Generator AI artykułów na bloga - <span>BlogScribe AI</span></h1>
        <div>
            <button onclick="changeLanguage('pl')" class="btn" style="margin-right: 5px;">PL</button>
            <button onclick="changeLanguage('en')" class="btn">EN</button>
        </div>
    </div>
    
    <!-- Nawigacja -->
    <div style="margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
        <button onclick="showTab('generator')" class="btn" id="generatorTabBtn" style="background: #4f46e5;">Generator</button>
        <button onclick="showTab('wordpress')" class="btn" id="wordpressTabBtn">WordPress</button>
    </div>
    
    <!-- Zakładka Generator -->
    <div id="generatorTab">
    
    <!-- Dodawanie tytułów -->
    <div class="form-group">
        <label for="title">Dodaj tytuł artykułu:</label>
        <div style="display: flex; gap: 10px;">
            <input type="text" id="title" placeholder="Wpisz tytuł artykułu">
            <button onclick="addTitle()" class="btn" style="margin-top: 0">Dodaj</button>
        </div>
    </div>
    
    <!-- Masowe dodawanie tytułów -->
    <div class="form-group">
        <label for="bulkTitles">Masowe dodawanie tytułów (jeden pod drugim lub oddzielone średnikami):</label>
        <textarea id="bulkTitles" rows="4" placeholder="Wpisz tytuły jeden pod drugim lub oddzielone średnikami (;)"></textarea>
        <button onclick="addBulkTitles()" class="btn">Dodaj tytuły masowo</button>
        <button onclick="clearAllTitles()" class="btn" style="background-color: #dc3545; margin-left: 10px;">🗑️ Usuń wszystkie tytuły</button>
    </div>
    
    <!-- Generowanie tytułów przez AI -->
    <div class="form-group">
        <label for="titleGenerationPrompt">Generowanie tytułów przez AI:</label>
        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
            <input type="text" id="titleGenerationPrompt" placeholder="Np. 'Artykuły o sprzątaniu domów i mieszkań'">
            <input type="number" id="titleGenerationCount" placeholder="Ilość" min="1" max="100" value="5" style="width: 100px;">
        </div>
        <button onclick="generateTitles()" class="btn">Wygeneruj tytuły</button>
    </div>
    
    <!-- Lista tytułów -->
    <div id="titlesList">
        <p>Dodane tytuły pojawią się tutaj</p>
    </div>
    
    <!-- Wybór dostawcy API -->
    <div class="form-group">
        <label for="apiProvider">Dostawca API:</label>
        <select id="apiProvider" onchange="toggleApiProviderFields()">
            <option value="openai">OpenAI</option>
            <option value="openrouter" selected>OpenRouter</option>
        </select>
    </div>
    
    <!-- Przyciski zarządzania konfiguracją -->
    <div class="form-group">
        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button onclick="saveConfig()" class="btn">Zapisz konfigurację</button>
            <button onclick="loadConfig()" class="btn">Wczytaj konfigurację</button>
            <button onclick="downloadConfigFile()" class="btn">Pobierz konfigurację</button>
            <button onclick="uploadConfigFile()" class="btn">Załaduj konfigurację</button>
        </div>
        <div id="configMessage" style="display: none; margin-top: 10px; padding: 10px; border-radius: 5px;"></div>
    </div>
    
    <!-- Klucz API OpenAI -->
    <div class="form-group" id="openaiKeyGroup">
        <label for="apiKey">Klucz API OpenAI:</label>
        <div style="display: flex; gap: 10px; position: relative;">
            <input type="password" id="apiKey" placeholder="Wprowadź swój klucz API OpenAI">
            <button type="button" class="btn" style="position: absolute; right: 120px; top: 10px; background: none; color: #666; padding: 0; margin: 0;" onclick="togglePasswordVisibility('apiKey')">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                </svg>
            </button>
            <button onclick="setApiKey(document.getElementById('apiKey').value, 'openai')" class="btn" style="margin-top: 0">Zapisz klucz</button>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="testOpenAIConnection(document.getElementById('apiKey').value)" class="btn">Testuj połączenie</button>
        </div>
        <small style="display: block; margin-top: 5px; color: #666;">
            Klucz jest zapisywany tylko w Twojej przeglądarce i nie jest wysyłany nigdzie poza API OpenAI.
        </small>
    </div>
    
    <!-- Wybór modelu OpenAI -->
    <div class="form-group" id="openaiModelGroup">
        <label for="openaiModel">Model OpenAI:</label>
        <select id="openaiModel">
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-4o">GPT-4o</option>
            <option value="gpt-4-turbo">GPT-4 Turbo</option>
        </select>
    </div>
    
    <!-- Klucz API OpenRouter -->
    <div class="form-group" id="openrouterKeyGroup" style="display: none;">
        <label for="openrouterApiKey">Klucz API OpenRouter:</label>
        <div style="display: flex; gap: 10px; position: relative;">
            <input type="password" id="openrouterApiKey" placeholder="Wprowadź swój klucz API OpenRouter">
            <button type="button" class="btn" style="position: absolute; right: 120px; top: 10px; background: none; color: #666; padding: 0; margin: 0;" onclick="togglePasswordVisibility('openrouterApiKey')">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                </svg>
            </button>
            <button onclick="setApiKey(document.getElementById('openrouterApiKey').value, 'openrouter')" class="btn" style="margin-top: 0">Zapisz klucz</button>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="testOpenRouterConnection(document.getElementById('openrouterApiKey').value)" class="btn">Testuj połączenie</button>
            <button onclick="testOpenRouterTitle()" class="btn" style="margin-left: 10px;">Testuj generowanie tytułu</button>
        </div>
        <small style="display: block; margin-top: 5px; color: #666;">
            Klucz jest zapisywany tylko w Twojej przeglądarce i nie jest wysyłany nigdzie poza API OpenRouter.
        </small>
    </div>
    
    <!-- Wybór modelu OpenRouter -->
    <div class="form-group" id="openrouterModelGroup" style="display: none;">
        <label for="openrouterModel">Model OpenRouter:</label>
        <select id="openrouterModel">
            <option value="">Ładowanie modeli...</option>
        </select>
    </div>
    
    <!-- Informacje dodatkowe -->
    <div class="form-group">
        <label for="additionalInfo">Informacje dodatkowe (zostaną dodane na końcu artykułu):</label>
        <textarea id="additionalInfo" rows="5" placeholder="Np. Czysto-Max oferuje profesjonalne usługi sprzątania w Pruszkowie. Zadzwoń: 502 123 456 lub wypełnij formularz na naszej stronie www.czysto-max.pl"></textarea>
        <small style="color: #666; font-size: 12px;">Te informacje zostaną dodane na końcu artykułu w sekcji kontaktowej, nie będą wplecione w treść.</small>
    </div>

    <div class="form-group">
        <label for="targetKeywords">🎯 Słowa kluczowe SEO (opcjonalne):</label>
        <textarea id="targetKeywords" rows="2" placeholder="Np. sprzątanie biur, mycie okien, czyszczenie dywanów, firma sprzątająca Warszawa"></textarea>
        <small style="color: #666; font-size: 12px;">Podaj główne słowa kluczowe, na które ma być zoptymalizowany artykuł. Jeśli pozostawisz puste, zostaną automatycznie wyodrębnione z tytułu.</small>
    </div>

    <!-- Ustawienia -->
    <div class="form-group">
        <label for="length">Długość artykułu:</label>
        <select id="length">
            <option value="short">Krótki (min. 400 słów)</option>
            <option value="medium" selected>Średni (min. 700 słów)</option>
            <option value="long">Długi (min. 1200 słów)</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="tone">Ton artykułu:</label>
        <select id="tone">
            <option value="professional" selected>Profesjonalny</option>
            <option value="casual">Przyjazny/konwersacyjny</option>
            <option value="expert">Ekspercki</option>
        </select>
    </div>

    <div class="form-group">
        <label>Opóźnienie między artykułami:</label>
        <div style="display: flex; gap: 10px;">
            <input type="number" id="delayMinutes" min="0" value="0" style="width: 80px;">
            <label for="delayMinutes" style="margin-top: 10px;">minut</label>
            <input type="number" id="delaySeconds" min="0" value="0" style="width: 80px;">
            <label for="delaySeconds" style="margin-top: 10px;">sekund</label>
        </div>
    </div>
    
    <!-- Przycisk generowania -->
    <button id="generateBtn" class="btn" onclick="generateArticles()">Generuj artykuły</button>
    <button id="stopBtn" class="btn" onclick="stopGeneration()" style="display: none; background-color: #f44336;">Zatrzymaj generowanie</button>
    <button id="resetFolderBtn" class="btn" onclick="resetArticlesFolder()" title="Zmień folder docelowy dla artykułów" style="background-color: #6c757d; margin-left: 10px;">📁 Zmień folder</button>

    <div style="margin-top: 10px; padding: 10px; background-color: #e8f5e9; border-radius: 5px; font-size: 14px;">
        <strong>💾 Automatyczne pobieranie:</strong> Artykuły będą automatycznie zapisywane do folderu "artykuly" na Twoim komputerze.
        Przy pierwszym generowaniu zostaniesz poproszony o wybór lokalizacji folderu.
    </div>
    
    <!-- Pole postępu generowania -->
    <div id="generationProgressContainer" style="display: none;">
        <div class="generation-progress" id="generationProgress"></div>
    </div>
    
    <!-- Miejsce na komunikaty o statusie -->
    <div id="statusContainer" style="display: none;"></div>
    
    <!-- Wyniki -->
    <div class="results" id="results">
        <!-- Tu pojawią się wygenerowane artykuły -->
    </div>
    
    </div>
    
    <!-- Zakładka WordPress -->
    <div id="wordpressTab" style="display: none;">
        <div class="wordpress-integration">
            <h2>Integracja z WordPress</h2>
            
            <div class="form-group">
                <label for="wpUrl" id="wpUrlLabel">Adres strony WordPress:</label>
                <input type="text" id="wpUrl" placeholder="https://twoja-strona.pl">
            </div>
            
            <div class="form-group">
                <label for="wpUsername" id="wpUsernameLabel">Nazwa użytkownika:</label>
                <input type="text" id="wpUsername" placeholder="Nazwa użytkownika WordPress">
            </div>
            
            <div class="form-group">
                <label for="wpPassword" id="wpPasswordLabel">Hasło API:</label>
                <input type="password" id="wpPassword" placeholder="Hasło aplikacji WordPress">
            </div>
            
            <button class="btn" onclick="connectToWordPress()" id="wpConnectBtn">Połącz z WordPress</button>
            
            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <button onclick="saveWordPressConfig()" class="btn">Zapisz konfigurację WP</button>
                <button onclick="loadWordPressConfig()" class="btn">Wczytaj konfigurację WP</button>
                <button onclick="downloadWordPressConfigFile()" class="btn">Pobierz konfigurację WP</button>
                <button onclick="uploadWordPressConfigFile()" class="btn">Załaduj konfigurację WP</button>
            </div>

            <div id="wpCategoriesContainer" style="display: none; margin-top: 20px;">
                <div class="form-group">
                    <label for="wpCategory" id="wpCategoryLabel">Wybierz kategorię:</label>
                    <select id="wpCategory">
                        <option value="">Ładowanie kategorii...</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label id="wpSelectArticlesLabel">Wybierz artykuły do publikacji:</label>
                    <button class="btn" onclick="selectArticlesFromComputer()" id="wpSelectFilesBtn">Wybierz pliki HTML</button>
                    <div class="file-list" id="selectedFiles">
                        <p id="wpNoFilesText">Wybrane pliki pojawią się tutaj</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label id="wpScheduleLabel">Harmonogram publikacji:</label>
                    <div class="schedule-options">
                        <label for="publishStartDate" id="wpStartDateLabel">Data rozpoczęcia:</label>
                        <input type="date" id="publishStartDate">
                        
                        <label for="publishInterval" id="wpIntervalLabel">Publikuj co (dni):</label>
                        <input type="number" id="publishInterval" min="0" value="0" style="width: 60px;">
                        
                        <label for="articlesPerDay" id="wpArticlesPerDayLabel">Artykułów dziennie:</label>
                        <input type="number" id="articlesPerDay" min="1" value="5" style="width: 60px;">
                    </div>
                    <small style="display: block; margin-top: 5px; color: #666;" id="wpIntervalHint">
                        Wartość 0 w polu "Publikuj co" oznacza natychmiastową publikację wszystkich artykułów.
                    </small>
                </div>
                
                <button class="btn" onclick="scheduleArticles()" id="wpScheduleBtn">Zaplanuj publikację artykułów</button>
            </div>
        </div>
    </div>

    <script>
        // Tablica przechowująca tytuły
        let titles = [];
        
        // Zmienne na klucze API
        let openAIApiKey = '';
        let openRouterApiKey = '';
        let currentProvider = 'openrouter';
        
        // Zmienne dla WordPress
        let wpCategories = [];
        let selectedArticleFiles = [];
        
        // Zmienna określająca, czy użytkownik jest zalogowany
        let isLoggedIn = true; // Ustawione na true, ponieważ nie ma logowania
        
        // Funkcja generująca 1000 loginów i haseł
        function generateUsers() {
            const userList = [];
            
            // Dodaj głównego administratora
            userList.push({
                username: "admin",
                password: "admin123"
            });
            
            // Generuj 999 losowych użytkowników
            for (let i = 1; i < 1000; i++) {
                const username = `user${i}`;
                const password = `pass${Math.floor(Math.random() * 10000)}`;
                
                userList.push({
                    username,
                    password
                });
            }
            
            return userList;
        }
        
        // Przełączanie pól dostawcy API
        function toggleApiProviderFields() {
            const provider = document.getElementById('apiProvider').value;
            currentProvider = provider;
            
            // Pokaż/ukryj odpowiednie pola
            if (provider === 'openai') {
                document.getElementById('openaiKeyGroup').style.display = 'block';
                document.getElementById('openaiModelGroup').style.display = 'block';
                document.getElementById('openrouterKeyGroup').style.display = 'none';
                document.getElementById('openrouterModelGroup').style.display = 'none';
            } else {
                document.getElementById('openaiKeyGroup').style.display = 'none';
                document.getElementById('openaiModelGroup').style.display = 'none';
                document.getElementById('openrouterKeyGroup').style.display = 'block';
                document.getElementById('openrouterModelGroup').style.display = 'block';
                // Wywołaj funkcję pobierającą modele OpenRouter, jeśli klucz jest dostępny
                if (openRouterApiKey) {
                    fetchAndPopulateOpenRouterModels();
                }
            }
        }
        
        // Dodawanie tytułu do listy
        function addTitle() {
            const titleInput = document.getElementById('title');
            let title = titleInput.value.trim();

            // Usuń cudzysłowy z początku i końca tytułu
            title = title.replace(/^["']|["']$/g, '');

            if (title) {
                titles.push(title);
                updateTitlesList();
                titleInput.value = '';
            } else {
                alert('Wprowadź tytuł artykułu');
            }
        }
        
        // Aktualizacja listy tytułów
        function updateTitlesList() {
            const titlesList = document.getElementById('titlesList');
            
            if (titles.length === 0) {
                titlesList.innerHTML = '<p>Dodane tytuły pojawią się tutaj</p>';
                return;
            }
            
            let html = '';
            titles.forEach((title, index) => {
                html += `
                <div class="title-item">
                    <span onclick="editTitle(${index})" style="cursor: pointer; flex-grow: 1;">${title}</span>
                    <div>
                        <button class="btn" style="padding: 5px 10px; margin: 0 5px;" onclick="editTitle(${index})">Edytuj</button>
                        <button class="remove-btn" onclick="removeTitle(${index})">Usuń</button>
                    </div>
                </div>`;
            });
            
            titlesList.innerHTML = html;
        }
        
        // Edycja tytułu
        function editTitle(index) {
            const newTitle = prompt("Edytuj tytuł:", titles[index]);
            if (newTitle !== null && newTitle.trim() !== "") {
                // Usuń cudzysłowy z edytowanego tytułu
                const cleanTitle = newTitle.trim().replace(/^["']|["']$/g, '');
                titles[index] = cleanTitle;
                updateTitlesList();
            }
        }
        
        // Masowe dodawanie tytułów
        function addBulkTitles() {
            const bulkTitlesInput = document.getElementById('bulkTitles').value.trim();
            
            if (!bulkTitlesInput) {
                alert('Wprowadź tytuły do masowego dodania');
                return;
            }
            
            // Sprawdź, czy tytuły są oddzielone średnikami czy nową linią
            let newTitles = [];
            if (bulkTitlesInput.includes(';')) {
                newTitles = bulkTitlesInput.split(';').map(t => t.trim()).filter(t => t);
            } else {
                newTitles = bulkTitlesInput.split('\n').map(t => t.trim()).filter(t => t);
            }

            // Usuń cudzysłowy z wszystkich tytułów
            newTitles = newTitles.map(title => title.replace(/^["']|["']$/g, ''));
            
            if (newTitles.length > 0) {
                titles = [...titles, ...newTitles];
                updateTitlesList();
                document.getElementById('bulkTitles').value = '';
                alert(`Dodano ${newTitles.length} tytułów`);
            }
        }
        
        // Zmienna globalna do kontrolowania zatrzymania generowania
        let stopGenerationFlag = false;

        // Funkcja do zatrzymywania generowania
        function stopGeneration() {
            stopGenerationFlag = true;
            updateGenerationProgress('Generowanie zostało zatrzymane przez użytkownika.');
            document.getElementById('generateBtn').disabled = false;
            document.getElementById('generateBtn').textContent = 'Generuj artykuły';
            document.getElementById('stopBtn').style.display = 'none';
        }

        // Generowanie tytułów przez AI
        async function generateTitles() {
            const prompt = document.getElementById('titleGenerationPrompt').value.trim();
            const count = parseInt(document.getElementById('titleGenerationCount').value);
            
            if (!prompt) {
                alert('Wprowadź opis tematyki dla generowanych tytułów');
                return;
            }
            
            if (isNaN(count) || count < 1 || count > 100) {
                alert('Wprowadź poprawną liczbę tytułów (1-100)');
                return;
            }
            
            // Sprawdź czy jest odpowiedni klucz API
            if (currentProvider === 'openai' && !openAIApiKey) {
                alert('Proszę wprowadzić klucz API OpenAI');
                return;
            } else if (currentProvider === 'openrouter' && !openRouterApiKey) {
                alert('Proszę wprowadzić klucz API OpenRouter');
                return;
            }
            
            // Zablokuj przycisk generowania
            const generateBtn = document.querySelector('button[onclick="generateTitles()"]');
            generateBtn.disabled = true;
            generateBtn.textContent = 'Generowanie...';
            
            try {
                // Przygotuj prompt dla AI
                const titleGenerationPrompt = `Wygeneruj ${count} unikalnych, atrakcyjnych tytułów artykułów na blog firmy sprzątającej na temat: ${prompt}.
                
                Tytuły powinny być:
                - Zróżnicowane tematycznie
                - Przyciągające uwagę
                - Zawierające słowa kluczowe związane ze sprzątaniem
                - Każdy tytuł w nowej linii
                - Bez numeracji
                
                Zwróć tylko listę tytułów, bez dodatkowego tekstu.`;
                
                // Wywołaj API
                let generatedContent;
                
                if (currentProvider === 'openai') {
                    const openaiModel = document.getElementById('openaiModel').value;
                    const response = await fetch('https://api.openai.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${openAIApiKey}`
                        },
                        body: JSON.stringify({
                            model: openaiModel,
                            messages: [
                                {
                                    "role": "system", 
                                    "content": "Jesteś ekspertem w tworzeniu chwytliwych tytułów artykułów na blog."
                                },
                                {
                                    "role": "user",
                                    "content": titleGenerationPrompt
                                }
                            ],
                            temperature: 0.8
                        })
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Błąd API: ${errorData.error?.message || response.status}`);
                    }
                    
                    const data = await response.json();
                    generatedContent = data.choices[0].message.content;
                } else {
                    // OpenRouter
                    const openrouterModel = document.getElementById('openrouterModel').value;
                    
                    updateGenerationProgress(`Używam nagłówka Authorization z Bearer token`);
                    
                    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    				method: 'POST',
    				headers: {
       				    'Content-Type': 'application/json',
                        'Authorization': `Bearer ${openRouterApiKey.trim()}`
                    },
                        body: JSON.stringify({
                            model: openrouterModel,
                            messages: [
                                {
                                    "role": "system", 
                                    "content": "Jesteś ekspertem w tworzeniu chwytliwych tytułów artykułów na blog."
                                },
                                {
                                    "role": "user",
                                    "content": titleGenerationPrompt
                                }
                            ],
                            temperature: 0.8
                        })
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Błąd API: ${errorData.error?.message || response.status}`);
                    }
                    
                    const data = await response.json();
                    generatedContent = data.choices[0].message.content;
                }
                
                // Przetwórz wygenerowane tytuły
                const generatedTitles = generatedContent
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line && !line.match(/^(\d+\.|\-|\*)/)) // Usuń numerację i punktory
                    .map(line => line.replace(/^["']|["']$/g, '')); // Usuń cudzysłowy na początku i końcu
                
                // Dodaj wygenerowane tytuły do listy
                if (generatedTitles.length > 0) {
                    titles = [...titles, ...generatedTitles];
                    updateTitlesList();
                    alert(`Wygenerowano i dodano ${generatedTitles.length} tytułów`); // Poprawka tutaj
                } else {
                    alert('Nie udało się wygenerować tytułów. Spróbuj zmienić opis.');
                }
            } catch (error) {
                console.error('Błąd generowania tytułów:', error);
                
                // Komunikat w odpowiednim języku
                const lang = document.getElementById('htmlRoot').lang;
                const errorText = lang === 'pl' ? 
                    `Błąd generowania tytułów: ${error.message}` : 
                    `Error generating titles: ${error.message}`;
                
                alert(errorText);
            } finally {
                // Odblokuj przycisk
                generateBtn.disabled = false;
                
                // Tekst przycisku w odpowiednim języku
                const lang = document.getElementById('htmlRoot').lang;
                generateBtn.textContent = lang === 'pl' ? 'Wygeneruj tytuły' : 'Generate titles';
            }
        }
        
        // Usunięcie tytułu z listy
        function removeTitle(index) {
            titles.splice(index, 1);
            updateTitlesList();
        }

        // Usuwanie wszystkich tytułów
        function clearAllTitles() {
            if (titles.length === 0) {
                alert('Lista tytułów jest już pusta');
                return;
            }

            if (confirm(`Czy na pewno chcesz usunąć wszystkie ${titles.length} tytułów?`)) {
                titles = [];
                updateTitlesList();
                alert('Wszystkie tytuły zostały usunięte');
            }
        }
        
        // Sprawdzanie poprawności klucza API
        function validateApiKey(key, provider) {
            if (!key) {
                return false;
            }
            
            // Sprawdź format klucza OpenAI
            if (provider === 'openai') {
                // Klucz OpenAI powinien zaczynać się od "sk-" i mieć odpowiednią długość
                return key.trim().startsWith('sk-') && key.trim().length > 20;
            } 
            // Sprawdź format klucza OpenRouter
            else if (provider === 'openrouter') {
                // Klucz OpenRouter powinien zaczynać się od "sk-or-" i mieć odpowiednią długość
                return key.trim().startsWith('sk-or-') && key.trim().length > 20;
            }
            
            return true;
        }
        
        // Zapisanie klucza API
        function setApiKey(key, provider) {
            if (!key) {
                alert(`Wprowadź klucz API ${provider === 'openai' ? 'OpenAI' : 'OpenRouter'}`);
                return;
            }
            
            // Sprawdź poprawność formatu klucza
            if (!validateApiKey(key, provider)) {
                const message = document.getElementById('htmlRoot').lang === 'pl' ? 
                    `Klucz API ${provider === 'openai' ? 'OpenAI' : 'OpenRouter'} ma nieprawidłowy format. Sprawdź klucz i spróbuj ponownie.` : 
                    `${provider === 'openai' ? 'OpenAI' : 'OpenRouter'} API key has invalid format. Check the key and try again.`;
                alert(message);
                return;
            }
            
            if (provider === 'openai') {
                openAIApiKey = key.trim();
                saveConfig();
            } else {
                openRouterApiKey = key.trim();
                // Testuj połączenie z OpenRouter przed zapisaniem
                testOpenRouterConnection(key.trim()).then(success => {
                    if (success) {
                        saveConfig();
                        // Załaduj modele OpenRouter po pomyślnym zapisaniu klucza
                        fetchAndPopulateOpenRouterModels();
                    }
                });
            }
        }
        
        // Testowanie połączenia z OpenRouter
        async function testOpenRouterConnection(apiKey) {
            try {
                if (!apiKey) {
                    const message = document.getElementById('htmlRoot').lang === 'pl' ? 
                        'Wprowadź klucz API OpenRouter przed testowaniem połączenia' : 
                        'Enter OpenRouter API key before testing connection';
                    alert(message);
                    return false;
                }
                
                updateGenerationProgress('Testowanie połączenia z OpenRouter...');
                
                // Używamy bezpośredniego podejścia z nagłówkiem Authorization
                updateGenerationProgress(`Używam nagłówka Authorization z Bearer token`);
                
                const response = await fetch('https://openrouter.ai/api/v1/models', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey.trim()}`
                    }
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    const errorMessage = data.error?.message || data.error?.code || response.status;
                    updateGenerationProgress(`Błąd połączenia z OpenRouter: ${errorMessage}`);
                    alert(`Błąd połączenia z OpenRouter: ${errorMessage}`);
                    return false;
                }
                
                updateGenerationProgress('Połączenie z OpenRouter działa poprawnie!');
                if (data.data && data.data.length) {
                    updateGenerationProgress(`Dostępne modele: ${data.data.length}`);
                    // Wyświetl listę dostępnych modeli
                    data.data.forEach(model => {
                        updateGenerationProgress(`Model: ${model.id} - ${model.name || 'Brak nazwy'}`);
                    });
                }
                alert('Połączenie z OpenRouter działa poprawnie!');

                // Załaduj modele OpenRouter po pomyślnym teście połączenia
                fetchAndPopulateOpenRouterModels();

                return true;
            } catch (error) {
                updateGenerationProgress(`Błąd testowania połączenia z OpenRouter: ${error.message}`);
                alert(`Błąd testowania połączenia z OpenRouter: ${error.message}`);
            }
        }
        
        // Testowanie generowania tytułu przez OpenRouter
        async function testOpenRouterTitle() {
            try {
                if (!openRouterApiKey) {
                    const message = document.getElementById('htmlRoot').lang === 'pl' ? 
                        'Najpierw zapisz klucz API OpenRouter' : 
                        'Save OpenRouter API key first';
                    alert(message);
                    return;
                }
                
                updateGenerationProgress('Testowanie generowania tytułu przez OpenRouter...');
                
                const openrouterModel = document.getElementById('openrouterModel').value;
                
                // Używamy bezpośredniego podejścia z nagłówkiem Authorization
                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${openRouterApiKey.trim()}`
                    },
                    body: JSON.stringify({
                        model: openrouterModel,
                        messages: [
                            {
                                "role": "system", 
                                "content": "Jesteś ekspertem w tworzeniu chwytliwych tytułów artykułów na blog."
                            },
                            {
                                "role": "user",
                                "content": "Wygeneruj jeden atrakcyjny tytuł artykułu na blog firmy sprzątającej."
                            }
                        ],
                        temperature: 0.8,
                        max_tokens: 50
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    const errorMessage = errorData.error?.message || errorData.error?.code || response.status;
                    updateGenerationProgress(`Błąd generowania tytułu: ${errorMessage}`);
                    alert(`Błąd generowania tytułu: ${errorMessage}`);
                    return;
                }
                
                const data = await response.json();
                const title = data.choices[0].message.content;
                
                updateGenerationProgress(`Wygenerowany tytuł: ${title}`);
                alert(`Wygenerowany tytuł: ${title}`);
            } catch (error) {
                updateGenerationProgress(`Błąd testowania generowania tytułu: ${error.message}`);
                alert(`Błąd testowania generowania tytułu: ${error.message}`);
            }
        }
        
        // Testowanie połączenia z OpenAI
        async function testOpenAIConnection(apiKey) {
            try {
                if (!apiKey) {
                    const message = document.getElementById('htmlRoot').lang === 'pl' ? 
                        'Wprowadź klucz API OpenAI przed testowaniem połączenia' : 
                        'Enter OpenAI API key before testing connection';
                    alert(message);
                    return false;
                }
                
                updateGenerationProgress('Testowanie połączenia z OpenAI...');
                
                const response = await fetch('https://api.openai.com/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    }
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    const errorMessage = errorData.error?.message || errorData.error?.code || response.status;
                    updateGenerationProgress(`Błąd połączenia z OpenAI: ${errorMessage}`);
                    alert(`Błąd połączenia z OpenAI: ${errorMessage}`);
                    return false;
                }
                
                const data = await response.json();
                updateGenerationProgress('Połączenie z OpenAI działa poprawnie!');
                alert('Połączenie z OpenAI działa poprawnie!');
                return true;
            } catch (error) {
                updateGenerationProgress(`Błąd testowania połączenia z OpenAI: ${error.message}`);
                alert(`Błąd testowania połączenia z OpenAI: ${error.message}`);
            }
        }
        
        // Załadowanie konfiguracji z localStorage
        async function loadConfig() {
            try {
                // Pobierz konfigurację z localStorage
                const configStr = localStorage.getItem('generator_config');
                if (!configStr) {
                    console.log('Nie znaleziono zapisanej konfiguracji');
                    return false;
                }
                
                const config = JSON.parse(configStr);
                
                // Załaduj klucze API
                if (config.openai_api_key) {
                    openAIApiKey = config.openai_api_key;
                    document.getElementById('apiKey').value = config.openai_api_key;
                }
                
                if (config.openrouter_api_key) {
                    openRouterApiKey = config.openrouter_api_key;
                    document.getElementById('openrouterApiKey').value = config.openrouter_api_key;
                }
                
                // Ustaw dostawcę API
                if (config.api_provider) {
                    document.getElementById('apiProvider').value = config.api_provider;
                    currentProvider = config.api_provider;
                    toggleApiProviderFields(); // To wywoła fetchAndPopulateOpenRouterModels() jeśli trzeba
                }
                
                // Załaduj model OpenAI
                if (config.openai_model) {
                    document.getElementById('openaiModel').value = config.openai_model;
                }
                
                // Załaduj model OpenRouter
                if (config.openrouter_model) {
                    document.getElementById('openrouterModel').value = config.openrouter_model;
                }

                // Załaduj informacje dodatkowe
                if (config.additional_info) {
                    document.getElementById('additionalInfo').value = config.additional_info;
                }

                // Załaduj słowa kluczowe
                if (config.target_keywords) {
                    document.getElementById('targetKeywords').value = config.target_keywords;
                }

                // Załaduj dane WordPress
                if (config.wordpress) {
                    document.getElementById('wpUrl').value = config.wordpress.url || '';
                    document.getElementById('wpUsername').value = config.wordpress.username || '';
                    document.getElementById('wpPassword').value = config.wordpress.password || '';
                    
                    // Jeśli są dane WordPress, spróbuj połączyć się automatycznie
                    if (config.wordpress.url && config.wordpress.username && config.wordpress.password) {
                        setTimeout(() => {
                            connectToWordPress();
                        }, 1000);
                    }
                }
                
                showConfigMessage('Konfiguracja załadowana pomyślnie', 'success');
                console.log('Konfiguracja załadowana pomyślnie');
                return true;
            } catch (error) {
                console.error('Błąd ładowania konfiguracji:', error);
                showConfigMessage(`Błąd ładowania konfiguracji: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Zapisanie konfiguracji do pliku
        async function saveConfig() {
            const config = {
                openai_api_key: openAIApiKey,
                openrouter_api_key: openRouterApiKey,
                api_provider: currentProvider,
                openai_model: document.getElementById('openaiModel').value,
                openrouter_model: document.getElementById('openrouterModel').value,
                additional_info: document.getElementById('additionalInfo').value,
                target_keywords: document.getElementById('targetKeywords').value,
                wordpress: {
                    url: document.getElementById('wpUrl').value,
                    username: document.getElementById('wpUsername').value,
                    password: document.getElementById('wpPassword').value
                }
            };
            
            try {
                // Zapisz konfigurację do localStorage
                localStorage.setItem('generator_config', JSON.stringify(config));
                
                // Zapisz konfigurację na serwerze
                const response = await fetch('save_config.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showConfigMessage('Konfiguracja zapisana pomyślnie', 'success');
                    console.log('Konfiguracja zapisana pomyślnie');
                } else {
                    showConfigMessage(`Błąd zapisywania konfiguracji na serwerze: ${result.message}`, 'warning');
                    console.warn('Konfiguracja zapisana tylko lokalnie:', result.message);
                }
                
                return true;
            } catch (error) {
                console.error('Błąd zapisywania konfiguracji:', error);
                showConfigMessage(`Konfiguracja zapisana tylko lokalnie. Błąd serwera: ${error.message}`, 'warning');
                return false;
            }
        }
        
        // Funkcja wyświetlająca komunikat o konfiguracji
        function showConfigMessage(message, type) {
            const messageElement = document.getElementById('configMessage');
            messageElement.textContent = message;
            messageElement.style.display = 'block';
            
            // Ustaw kolor tła w zależności od typu komunikatu
            if (type === 'success') {
                messageElement.style.backgroundColor = '#d4edda';
                messageElement.style.color = '#155724';
                messageElement.style.border = '1px solid #c3e6cb';
            } else if (type === 'error') {
                messageElement.style.backgroundColor = '#f8d7da';
                messageElement.style.color = '#721c24';
                messageElement.style.border = '1px solid #f5c6cb';
            } else if (type === 'warning') {
                messageElement.style.backgroundColor = '#fff3cd';
                messageElement.style.color = '#856404';
                messageElement.style.border = '1px solid #ffeeba';
            }
            
            // Ukryj komunikat po 5 sekundach
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
        
        // Pobieranie konfiguracji jako plik
        function downloadConfigFile() {
            const config = {
                openai_api_key: openAIApiKey,
                openrouter_api_key: openRouterApiKey,
                api_provider: currentProvider,
                openai_model: document.getElementById('openaiModel').value,
                openrouter_model: document.getElementById('openrouterModel').value,
                additional_info: document.getElementById('additionalInfo').value,
                target_keywords: document.getElementById('targetKeywords').value,
                wordpress: {
                    url: document.getElementById('wpUrl').value,
                    username: document.getElementById('wpUsername').value,
                    password: document.getElementById('wpPassword').value
                }
            };
            
            const configStr = JSON.stringify(config, null, 2);
            const blob = new Blob([configStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'generator_config.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showConfigMessage('Plik konfiguracyjny został pobrany', 'success');
        }
        
        // Wczytywanie konfiguracji z pliku
        function uploadConfigFile() {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.json';
            
            fileInput.addEventListener('change', async function() {
                if (this.files && this.files[0]) {
                    try {
                        const file = this.files[0];
                        const fileContent = await readFileContent(file);
                        const config = JSON.parse(fileContent);
                        
                        // Załaduj klucze API
                        if (config.openai_api_key) {
                            openAIApiKey = config.openai_api_key;
                            document.getElementById('apiKey').value = config.openai_api_key;
                        }
                        
                        if (config.openrouter_api_key) {
                            openRouterApiKey = config.openrouter_api_key;
                            document.getElementById('openrouterApiKey').value = config.openrouter_api_key;
                        }
                        
                        // Ustaw dostawcę API
                        if (config.api_provider) {
                            document.getElementById('apiProvider').value = config.api_provider;
                            currentProvider = config.api_provider;
                            toggleApiProviderFields();

                            // Jeśli to OpenRouter i mamy klucz API, załaduj modele
                            if (config.api_provider === 'openrouter' && config.openrouter_api_key) {
                                setTimeout(() => {
                                    fetchAndPopulateOpenRouterModels();
                                }, 500);
                            }
                        }
                        
                        // Załaduj model OpenAI
                        if (config.openai_model) {
                            document.getElementById('openaiModel').value = config.openai_model;
                        }
                        
                        // Załaduj model OpenRouter
                        if (config.openrouter_model) {
                            document.getElementById('openrouterModel').value = config.openrouter_model;
                        }

                        // Załaduj informacje dodatkowe
                        if (config.additional_info) {
                            document.getElementById('additionalInfo').value = config.additional_info;
                        }

                        // Załaduj słowa kluczowe
                        if (config.target_keywords) {
                            document.getElementById('targetKeywords').value = config.target_keywords;
                        }

                        // Załaduj dane WordPress
                        if (config.wordpress) {
                            document.getElementById('wpUrl').value = config.wordpress.url || '';
                            document.getElementById('wpUsername').value = config.wordpress.username || '';
                            document.getElementById('wpPassword').value = config.wordpress.password || '';
                        }
                        
                        // Zapisz konfigurację do localStorage
                        localStorage.setItem('generator_config', JSON.stringify(config));
                        
                        showConfigMessage('Konfiguracja załadowana z pliku pomyślnie', 'success');
                    } catch (error) {
                        console.error('Błąd wczytywania pliku konfiguracyjnego:', error);
                        showConfigMessage(`Błąd wczytywania pliku konfiguracyjnego: ${error.message}`, 'error');
                    }
                }
            });
            
            fileInput.click();
        }
        
        // Utworzenie statusu dla każdego artykułu
        function createArticleStatus(titlesList) {
            const statusContainer = document.getElementById('statusContainer');
            statusContainer.style.display = 'block';
            statusContainer.innerHTML = `
                <div class="loading-container">
                    <h3>Generowanie artykułów w toku...</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    <p id="progressText">Przygotowanie do generowania artykułów (0/${titlesList.length})</p>
                    <div id="articleStatuses"></div>
                    <p class="time-remaining" id="timeRemaining">Szacowany czas: obliczanie...</p>
                </div>
            `;
            
            const articleStatuses = document.getElementById('articleStatuses');
            titlesList.forEach((title, index) => {
                articleStatuses.innerHTML += `
                    <div class="status-indicator">
                        <div class="status-dot status-pending" id="status-dot-${index}"></div>
                        <div class="status-text" id="status-text-${index}">${title} - oczekuje</div>
                    </div>
                `;
            });
        }
        
        // Aktualizacja statusu generowania
        function updateArticleStatus(index, status, message) {
            const statusDot = document.getElementById(`status-dot-${index}`);
            const statusText = document.getElementById(`status-text-${index}`);
            
            // Usunięcie poprzednich klas statusu
            statusDot.classList.remove('status-pending', 'status-generating', 'status-complete', 'status-error');
            
            // Dodanie nowej klasy statusu
            statusDot.classList.add(`status-${status}`);
            
            // Aktualizacja tekstu statusu
            statusText.textContent = message;
        }
        
        // Aktualizacja całkowitego postępu
        function updateTotalProgress(completed, total) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const timeRemaining = document.getElementById('timeRemaining');
            
            // Oblicz procent ukończenia
            const percentage = Math.round((completed / total) * 100);
            
            // Aktualizacja paska postępu
            progressFill.style.width = `${percentage}%`;
            
            // Aktualizacja tekstu postępu
            progressText.textContent = `Generowanie artykułów (${completed}/${total})`;
            
            // Aktualizacja szacowanego czasu
            if (completed === 0) {
                timeRemaining.textContent = 'Szacowany czas: obliczanie...';
            } else {
                // Szacowanie pozostałego czasu (zakładając średnio 30 sekund na artykuł)
                const remainingArticles = total - completed;
                const estimatedSecondsPerArticle = 30; 
                const remainingSeconds = remainingArticles * estimatedSecondsPerArticle;
                
                // Formatowanie czasu
                let timeText;
                if (remainingSeconds < 60) {
                    timeText = `${remainingSeconds} sekund`;
                } else {
                    const minutes = Math.floor(remainingSeconds / 60);
                    const seconds = remainingSeconds % 60;
                    timeText = `${minutes} minut${minutes === 1 ? 'ę' : minutes < 5 ? 'y' : ''} ${seconds > 0 ? `i ${seconds} sekund` : ''}`;
                }
                
                timeRemaining.textContent = `Szacowany pozostały czas: ${timeText}`;
            }
        }
        
        // Wyświetlanie komunikatu o błędzie
        function showError(message) {
            const statusContainer = document.getElementById('statusContainer');
            statusContainer.innerHTML = `
                <div class="error-container">
                    <h3>Wystąpił błąd</h3>
                    <p>${message}</p>
                    <button class="btn" onclick="document.getElementById('statusContainer').style.display='none'">OK</button>
                </div>
            `;
            
            // Odblokuj przycisk generowania
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = false;
        }
        
        // Funkcja do debugowania nagłówków
        function debugHeaders(headers) {
            try {
                const headersObj = {};
                if (headers instanceof Headers) {
                    headers.forEach((value, key) => {
                        headersObj[key] = value;
                    });
                } else if (typeof headers === 'object') {
                    Object.keys(headers).forEach(key => {
                        headersObj[key] = headers[key];
                    });
                }
                console.log('Nagłówki zapytania:', headersObj);
                updateGenerationProgress(`Nagłówki zapytania: ${JSON.stringify(headersObj)}`);
                return headersObj;
            } catch (error) {
                console.error('Błąd podczas debugowania nagłówków:', error);
                updateGenerationProgress(`Błąd podczas debugowania nagłówków: ${error.message}`);
                return {};
            }
        }
        
        // Funkcja z ponownymi próbami przy błędach limitów
        async function retryableRequest(requestFn, maxRetries = 3, delay = 5000) {
            let lastError;
            
            for (let attempt = 0; attempt < maxRetries; attempt++) {
                try {
                    return await requestFn();
                } catch (error) {
                    lastError = error;
                    
                    // Sprawdź czy to błąd limitów
                    if (error.message && (
                        error.message.includes('rate limit') || 
                        error.message.includes('too many requests') ||
                        error.message.includes('overloaded')
                    )) {
                        console.log(`Próba ${attempt + 1}/${maxRetries} nie udała się z powodu limitów. Czekam ${delay/1000}s przed ponowną próbą...`);
                        
                        // Poczekaj przed kolejną próbą
                        await new Promise(resolve => setTimeout(resolve, delay));
                        
                        // Zwiększ opóźnienie dla kolejnej próby
                        delay *= 2;
                    } else {
                        // Jeśli to inny błąd, nie próbuj ponownie
                        throw error;
                    }
                }
            }
            
            throw lastError;
        }
        
        // Funkcja generująca artykuł przy pomocy API (OpenAI lub OpenRouter)
        async function generateArticleWithAI(title, additionalInfo, length, tone, targetKeywords) {
            // Definicja funkcji wykonującej zapytanie
            const makeRequest = async () => {
                const lengthMap = {
                    short: "minimum 400 słów (300-500 słów)",
                    medium: "minimum 700 słów (600-900 słów)",
                    long: "minimum 1200 słów (1200-1800 słów)"
                };
                
                const toneMap = {
                    professional: "profesjonalny, rzeczowy",
                    casual: "przyjazny, konwersacyjny",
                    expert: "ekspercki, autorytatywny"
                };
                
                const prompt = `Napisz EKSPERTOWY ARTYKUŁ SEO zoptymalizowany pod Google AI, ChatGPT, Perplexity i tradycyjne wyszukiwarki na blog firmy sprzątającej o tytule: "${title}".

                🎯 SŁOWA KLUCZOWE DO OPTYMALIZACJI: ${targetKeywords || 'Automatycznie wyodrębnij główne słowa kluczowe z tytułu'}
                📊 WYMAGANIA DŁUGOŚCI: ${lengthMap[length]} - KRYTYCZNE: Artykuł musi osiągnąć minimalną liczbę słów!
                🎨 TON: ${toneMap[tone]}

                🏢 INFORMACJE DODATKOWE (zostaną dodane na końcu artykułu):
                ${additionalInfo || 'Brak dodatkowych informacji'}

                🤖 OPTYMALIZACJA POD AI SEARCH ENGINES (Google AI, ChatGPT, Perplexity):

                1. STRUKTURA ODPOWIEDZI AI:
                   - Bezpośrednie odpowiedzi na pytania w pierwszych akapitach
                   - Jasne definicje i wyjaśnienia terminów
                   - Konkretne dane liczbowe i fakty
                   - Porównania i zestawienia
                   - Praktyczne kroki i instrukcje

                2. FORMATOWANIE POD AI:
                   - Używaj jasnych nagłówków pytań (Jak?, Dlaczego?, Co to znaczy?)
                   - Twórz listy z konkretnymi punktami
                   - Dodawaj podsumowania sekcji
                   - Używaj słów sygnałowych: "najważniejsze", "kluczowe", "główne"
                   - Strukturyzuj informacje hierarchicznie

                3. TREŚĆ PRZYJAZNA AI:
                   - Odpowiadaj na pytania typu "co", "jak", "dlaczego", "kiedy", "gdzie"
                   - Używaj synonimów i wariantów terminów
                   - Dodawaj kontekst i tło tematyczne
                   - Łącz tematy powiązane
                   - Przewiduj pytania następne użytkowników

                🔍 STRATEGIA SEO TRADYCYJNEGO - WYMAGANIA OBOWIĄZKOWE:

                1. SŁOWA KLUCZOWE:
                   - Główne słowo kluczowe: użyj 8-12 razy naturalnie w tekście
                   - Synonimy i warianty: minimum 15 różnych określeń powiązanych z tematem
                   - Long-tail keywords: włącz 5-8 fraz długoogonowych
                   - LSI keywords: użyj słów semantycznie powiązanych z tematem

                2. STRUKTURA HTML SEO:
                   - H1: Tytuł główny (dokładnie jak podany tytuł)
                   - H2: 2-3 główne sekcje z słowami kluczowymi
                   - H3: 4-8 podsekcji z wariantami słów kluczowych
                   - H4: Opcjonalnie dla szczegółowych punktów

                3. OPTYMALIZACJA TREŚCI:
                   - Pierwsze 100 słów: zawrzyj główne słowo kluczowe 2-3 razy
                   - Meta description w pierwszym akapicie (155-160 znaków)
                   - Gęstość słów kluczowych: 1-2% dla głównego, 0.5-1% dla pozostałych
                   - Użyj <strong> dla ważnych słów kluczowych (nie przesadzaj)
                   - Dodaj <em> dla synonimów i wariantów

                4. STRUKTURA ARTYKUŁU ZOPTYMALIZOWANA POD AI:
                   - Wprowadzenie (150-200 słów): Bezpośrednia odpowiedź + problem + rozwiązanie + zapowiedź
                   - "Co to jest [temat]?" (H2): Jasna definicja i podstawy
                   - "Jak [wykonać czynność]?" (H2): Krok po kroku instrukcje
                   - "Dlaczego [temat] jest ważny?" (H2): Korzyści i znaczenie
                   - "Kiedy [stosować/robić]?" (H2): Timing i okoliczności
                   - "Ile kosztuje [usługa]?" (H2): Konkretne informacje cenowe
                   - FAQ (H2): 5-8 najczęstszych pytań z kompletnymi odpowiedziami
                   - "Podsumowanie - najważniejsze informacje" (H2): Kluczowe wnioski + CTA

                5. ELEMENTY ZWIĘKSZAJĄCE ZAANGAŻOWANIE:
                   - Listy numerowane dla kroków/procesów
                   - Listy punktowane dla zalet/wad
                   - Tabele porównawcze (jeśli pasują do tematu)
                   - Cytaty/statystyki (wymyśl wiarygodne)
                   - Przykłady praktyczne i case studies

                6. OPTYMALIZACJA TECHNICZNA:
                   - Długość akapitów: maksymalnie 3-4 zdania
                   - Zdania: różnorodna długość, średnio 15-20 słów
                   - Słowa przejściowe: używaj "ponadto", "dodatkowo", "w rezultacie" itp.
                   - Wewnętrzne linkowanie: wspomnij o powiązanych tematach

                7. E-A-T (Expertise, Authoritativeness, Trustworthiness):
                   - Prezentuj się jako ekspert w branży sprzątającej
                   - Używaj konkretnych danych i faktów
                   - Odwołuj się do doświadczenia firmy
                   - Wspominaj certyfikaty, standardy, normy

                8. INTENCJA WYSZUKIWANIA:
                   - Odpowiedz na wszystkie możliwe pytania użytkownika
                   - Przewiduj kolejne pytania i potrzeby
                   - Dostarczaj praktyczne, wykonalne rozwiązania
                   - Uwzględnij różne poziomy zaawansowania czytelników

                9. LOKALNE SEO (jeśli dotyczy):
                   - Wspominaj miasta/regiony obsługiwane przez firmę
                   - Używaj określeń geograficznych naturalnie w tekście
                   - Odwołuj się do lokalnych potrzeb i specyfiki

                10. CALL-TO-ACTION:
                    - Minimum 2-3 naturalne zachęty do kontaktu w treści artykułu
                    - Różnorodne CTA: telefon, formularz, wycena
                    - Podkreślaj korzyści z wyboru firmy
                    - NA KOŃCU artykułu dodaj sekcję kontaktową z informacjami dodatkowymi

                ⚡ WYMAGANIA JAKOŚCI GOOGLE I AI:
                - Unikalne, oryginalne treści (zero duplikowania)
                - Wysoką wartość dla użytkownika
                - Kompleksowe omówienie tematu
                - Aktualne i dokładne informacje
                - Łatwe do czytania i zrozumienia
                - Mobile-friendly struktura
                - Bezpośrednie odpowiedzi na pytania użytkowników
                - Strukturyzowane dane i informacje
                - Logiczny przepływ informacji

                📝 WYMAGANIA FORMATOWANIA POD WORDPRESS:
                - Używaj tylko standardowych tagów HTML: h1, h2, h3, p, ul, ol, li, strong, em
                - NIE używaj div, span, class, style ani innych atrybutów
                - Każdy akapit w osobnych tagach <p>
                - Listy zawsze w <ul><li> lub <ol><li>
                - Nagłówki hierarchicznie: H1 (tytuł), H2 (główne sekcje), H3 (podsekcje)
                - Tekst pogrubiony tylko <strong>, kursywa tylko <em>
                - Bez dodatkowych stylów CSS - czysty HTML

                🚀 CEL: Artykuł ma być NAJLEPSZYM WYNIKIEM dla AI i tradycyjnych wyszukiwarek!

                📝 STRUKTURA KOŃCOWA ARTYKUŁU:
                1. Napisz kompletny artykuł zgodnie z powyższymi wymaganiami
                2. Na samym końcu dodaj sekcję kontaktową z informacjami dodatkowymi
                3. Jeśli są informacje dodatkowe, umieść je w sekcji "Skontaktuj się z nami" lub podobnej
                4. Jeśli brak informacji dodatkowych, zakończ artykuł standardowym podsumowaniem

                Napisz artykuł, który zdominuje TOP 10 Google dla głównego słowa kluczowego i dziesiątek powiązanych fraz:`;
                
                const systemMessage = "Jesteś EKSPERTEM SEO i AI-OPTIMIZED copywriterem specjalizującym się w artykułach dla firm sprzątających. Tworzysz artykuły, które DOMINUJĄ w Google TOP 10 oraz są PREFEROWANE przez AI search engines (Google AI, ChatGPT, Perplexity). Masz głęboką wiedzę o algorytmach Google, E-A-T, intencji wyszukiwania, optymalizacji treści POD AI oraz formatowaniu pod WordPress. ZAWSZE tworzysz treści o najwyższej jakości, które przyciągają tysiące wyświetleń i są cytowane przez AI. Każdy artykuł to MASTERPIECE SEO w języku polskim, perfekcyjnie zoptymalizowany pod pozycjonowanie tradycyjne I AI. Osiągasz 100% wymaganej długości i jakości. Używasz TYLKO czystego HTML bez CSS.";
                
                let requestOptions;
                let apiUrl;
                
                if (currentProvider === 'openai') {
                    // Konfiguracja dla OpenAI
                    apiUrl = 'https://api.openai.com/v1/chat/completions';
                    const openaiModel = document.getElementById('openaiModel').value;
                    requestOptions = {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${openAIApiKey}`
                        },
                        body: JSON.stringify({
                            model: openaiModel,
                            messages: [
                                {
                                    "role": "system", 
                                    "content": systemMessage
                                },
                                {
                                    "role": "user",
                                    "content": prompt
                                }
                            ],
                            temperature: 0.7,
                            max_tokens: length === 'long' ? 8000 : length === 'medium' ? 5000 : 3000
                        })
                    };
                } else {
                    // Konfiguracja dla OpenRouter
                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
                    const openrouterModel = document.getElementById('openrouterModel').value;
                    
                    // Używamy bezpośredniego podejścia z nagłówku Authorization
                    updateGenerationProgress(`Używam nagłówka Authorization z Bearer token`);
                    
                    requestOptions = {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${openRouterApiKey.trim()}`
                        },
                        body: JSON.stringify({
                            model: openrouterModel,
                            messages: [
                                {
                                    "role": "system", 
                                    "content": systemMessage
                                },
                                {
                                    "role": "user",
                                    "content": prompt
                                }
                            ],
                            temperature: 0.7,
                            max_tokens: length === 'long' ? 8000 : length === 'medium' ? 5000 : 3000
                        })
                    };
                }
        
                // Aktualizuj pole postępu generowania
                const modelName = currentProvider === 'openai' ? 'OpenAI' : document.getElementById('openrouterModel').value;
                updateGenerationProgress(`Wysyłanie zapytania do modelu ${modelName}...`);
                
                // Debuguj nagłówki jeśli to OpenRouter
                if (currentProvider === 'openrouter') {
                    debugHeaders(requestOptions.headers);
                }
                
                const response = await fetch(apiUrl, requestOptions);
                
                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (e) {
                        // Jeśli odpowiedź nie jest poprawnym JSON
                        const responseText = await response.text();
                        errorData = { error: { message: responseText || 'Nieznany błąd' } };
                    }
                    
                    const errorMessage = errorData.error?.message || errorData.error?.code || response.status;
                    const errorDetails = JSON.stringify(errorData, null, 2);
                    
                    // Komunikat w odpowiednim języku
                    const lang = document.getElementById('htmlRoot').lang;
                    const apiErrorText = lang === 'pl' ? 'Błąd API' : 'API Error';
                    const detailsText = lang === 'pl' ? 'Szczegóły błędu' : 'Error details';
                    
                    updateGenerationProgress(`${apiErrorText}: ${errorMessage}`);
                    updateGenerationProgress(`${detailsText}: ${errorDetails}`);
                    throw new Error(`${apiErrorText}: ${errorMessage}`);
                }
        
                updateGenerationProgress(`Otrzymano odpowiedź, przetwarzanie...`);
                const data = await response.json();
                return data.choices[0].message.content;
            };
            
            // Użyj funkcji z ponownymi próbami
            return await retryableRequest(makeRequest);
        }
        
        // Funkcja aktualizująca pole postępu generowania
        function updateGenerationProgress(message) {
            const progressContainer = document.getElementById('generationProgressContainer');
            const progressElement = document.getElementById('generationProgress');
            
            // Pokaż kontener, jeśli jest ukryty
            progressContainer.style.display = 'block';
            
            // Dodaj nową wiadomość
            const messageElement = document.createElement('div');
            messageElement.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            progressElement.appendChild(messageElement);
            
            // Przewiń na dół
            progressElement.scrollTop = progressElement.scrollHeight;
        }
        
        // Generowanie artykułów
        async function generateArticles() {
            // Sprawdź czy użytkownik jest zalogowany
            if (!isLoggedIn) {
                alert('Musisz się zalogować, aby korzystać z tej funkcji');
                return;
            }
            
            // Sprawdź czy jest odpowiedni klucz API
            if (currentProvider === 'openai' && !openAIApiKey) {
                alert('Proszę wprowadzić klucz API OpenAI');
                return;
            } else if (currentProvider === 'openrouter' && !openRouterApiKey) {
                alert('Proszę wprowadzić klucz API OpenRouter');
                return;
            }
            
            // Sprawdź czy są tytuły
            if (titles.length === 0) {
                alert('Dodaj przynajmniej jeden tytuł artykułu');
                return;
            }
            
            // Zablokuj przycisk, aby uniknąć wielokrotnego generowania
            const generateBtn = document.getElementById('generateBtn');
            const stopBtn = document.getElementById('stopBtn');
            generateBtn.disabled = true;
            stopBtn.style.display = 'inline-block'; // Pokaż przycisk zatrzymania
            
            const additionalInfo = document.getElementById('additionalInfo').value;
            const length = document.getElementById('length').value;
            const tone = document.getElementById('tone').value;
            const targetKeywords = document.getElementById('targetKeywords').value;

            // Pobierz wartości opóźnienia
            const delayMinutes = parseInt(document.getElementById('delayMinutes').value);
            const delaySeconds = parseInt(document.getElementById('delaySeconds').value);
            const totalDelayMilliseconds = (delayMinutes * 60 + delaySeconds) * 1000;
            
            // Przygotowanie containerów na status i wyniki
            createArticleStatus(titles);
            const results = document.getElementById('results');
            results.innerHTML = '';
            
            let completedArticles = 0;
            let errorCount = 0;
            
            // Wyczyść pole postępu generowania
            document.getElementById('generationProgress').innerHTML = '';
            updateGenerationProgress('Rozpoczynam generowanie artykułów...');

            stopGenerationFlag = false; // Resetuj flagę przed rozpoczęciem generowania
            
            // Generuj artykuły sekwencyjnie (jeden po drugim)
            for (let i = 0; i < titles.length; i++) {
                if (stopGenerationFlag) { // Sprawdź flagę zatrzymania
                    updateGenerationProgress('Generowanie przerwane.');
                    break; // Wyjdź z pętli
                }
                // Aktualizuj status artykułu do "generowanie"
                updateArticleStatus(i, 'generating', `${titles[i]} - generowanie w toku...`);
                updateGenerationProgress(`Rozpoczynam generowanie artykułu "${titles[i]}"...`);
                
                try {
                    // Rzeczywiste generowanie artykułu poprzez API
                    const article = await generateArticleWithAI(
                        titles[i],
                        additionalInfo,
                        length,
                        tone,
                        targetKeywords
                    );
                    
                    updateGenerationProgress(`Artykuł "${titles[i]}" wygenerowany pomyślnie.`);
                    
                    // Automatycznie pobierz artykuł jako plik HTML
                    downloadArticleDirectly(titles[i], article);
                    updateGenerationProgress(`Artykuł "${titles[i]}" zapisany jako plik HTML.`);
                    
                    // Dodaj informację o pobraniu do wyników
                    const downloadInfoElement = document.createElement('div');
                    downloadInfoElement.className = 'article';
                    downloadInfoElement.innerHTML = `
                        <h3>Artykuł "${titles[i]}" został automatycznie pobrany</h3>
                        <p>Plik został zapisany jako: ${titles[i].replace(/\s+/g, '-').toLowerCase()}.html</p>
                    `;
                    
                    results.appendChild(downloadInfoElement);
                    
                    // Aktualizuj status artykułu do "zakończone"
                    updateArticleStatus(i, 'complete', `${titles[i]} - wygenerowano pomyślnie`);
                    
                    // Aktualizuj ogólny postęp
                    completedArticles++;
                    updateTotalProgress(completedArticles, titles.length);
                    
                } catch (error) {
                    // Obsługa błędu generowania artykułu
                    const lang = document.getElementById('htmlRoot').lang;
                    const unknownError = lang === 'pl' ? 'Nieznany błąd' : 'Unknown error';
                    const errorMessage = lang === 'pl' ? 
                        `${titles[i]} - błąd generowania: ${error.message || unknownError}` : 
                        `${titles[i]} - generation error: ${error.message || unknownError}`;
                    
                    updateArticleStatus(i, 'error', errorMessage);
                    errorCount++;
                    
                    console.error(`Błąd generowania dla tytułu "${titles[i]}":`, error);
                }
                
                // Dodaj opóźnienie, jeśli jest ustawione i to nie jest ostatni artykuł
                if (totalDelayMilliseconds > 0 && i < titles.length - 1) {
                    updateGenerationProgress(`Czekam ${delayMinutes}m ${delaySeconds}s przed generowaniem kolejnego artykułu...`);
                    await new Promise(resolve => setTimeout(resolve, totalDelayMilliseconds));
                }
            }
            
            // Po zakończeniu generowania wszystkich artykułów
            setTimeout(() => {
                const statusContainer = document.getElementById('statusContainer');
                
                if (completedArticles > 0) {
                    // Jeśli przynajmniej jeden artykuł został wygenerowany pomyślnie
                    statusContainer.innerHTML = `
                        <div class="loading-container" style="background-color: #e8f5e9;">
                            <h3>Generowanie zakończone</h3>
                            <p>Pomyślnie wygenerowano i pobrano ${completedArticles} z ${titles.length} artykułów.</p>
                            <button class="btn" onclick="document.getElementById('statusContainer').style.display='none'">OK</button>
                        </div>
                    `;
                } else {
                    // Jeśli nie udało się wygenerować żadnego artykułu
                    showError('Nie udało się wygenerować artykułów. Sprawdź konsolę przeglądarki, aby zobaczyć szczegóły błędów.');
                }
                
                // Odblokuj przycisk generowania
                generateBtn.disabled = false;
                stopBtn.style.display = 'none'; // Ukryj przycisk zatrzymania
            }, 1000);
        }
        
        // Kopiowanie artykułu jako HTML
        function copyArticle(button) {
            const article = button.closest('.article');
            const title = article.querySelector('h2').outerHTML;
            const content = article.querySelector('.article-content').innerHTML;
            
            const htmlToClipboard = `<article class="blog-post">
                ${title}
                <div class="blog-content">
                    ${content}
                </div>
            </article>`;
            
            navigator.clipboard.writeText(htmlToClipboard).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Skopiowano!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }
        
        // Pobieranie artykułu jako plik HTML (z przycisku)
        function downloadArticle(title, button) {
            const article = button.closest('.article');
            const titleHTML = article.querySelector('h2').outerHTML;
            const content = article.querySelector('.article-content').innerHTML;
            
            downloadHTMLFile(title, titleHTML, content);
            
            const originalText = button.textContent;
            button.textContent = 'Pobrano!';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
        
        // Pobieranie artykułu bezpośrednio (automatycznie)
        async function downloadArticleDirectly(title, content) {
            const titleHTML = `<h2>${title}</h2>`;

            // Sprawdź czy przeglądarka obsługuje File System Access API
            if ('showDirectoryPicker' in window) {
                try {
                    await downloadToArticlesFolder(title, titleHTML, content);
                } catch (error) {
                    console.log('Nie udało się zapisać do folderu, używam standardowego pobierania:', error);
                    downloadHTMLFile(title, titleHTML, content);
                }
            } else {
                // Fallback do standardowego pobierania
                downloadHTMLFile(title, titleHTML, content);
            }
        }
        
        // Zmienna globalna do przechowywania handle folderu
        let articlesDirectoryHandle = null;

        // Funkcja do zapisywania artykułów do dedykowanego folderu
        async function downloadToArticlesFolder(title, titleHTML, content) {
            try {
                // Jeśli nie mamy jeszcze handle folderu, poproś użytkownika o wybór
                if (!articlesDirectoryHandle) {
                    // Poproś użytkownika o wybór folderu głównego
                    const directoryHandle = await window.showDirectoryPicker({
                        mode: 'readwrite'
                    });

                    // Sprawdź czy folder "artykuly" już istnieje
                    try {
                        articlesDirectoryHandle = await directoryHandle.getDirectoryHandle('artykuly');
                    } catch (error) {
                        // Jeśli folder nie istnieje, utwórz go
                        articlesDirectoryHandle = await directoryHandle.getDirectoryHandle('artykuly', {
                            create: true
                        });
                    }
                }

                // Przygotuj zawartość pliku HTML zoptymalizowaną pod WordPress
                const htmlContent = `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; color: #333; }
        h1, h2, h3 { color: #2c3e50; margin-top: 30px; margin-bottom: 15px; }
        h1 { font-size: 2.2em; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { font-size: 1.8em; color: #34495e; }
        h3 { font-size: 1.4em; color: #7f8c8d; }
        p { margin-bottom: 15px; text-align: justify; }
        ul, ol { margin-bottom: 15px; padding-left: 30px; }
        li { margin-bottom: 8px; }
        strong { font-weight: bold; color: #2c3e50; }
        em { font-style: italic; color: #7f8c8d; }
        .wordpress-note { background: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0; }
        .wordpress-content { background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; margin: 20px 0; }
        pre { background: #f4f4f4; padding: 15px; overflow-x: auto; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="wordpress-note">
        <strong>📝 ARTYKUŁ GOTOWY DO WORDPRESS</strong><br>
        Skopiuj zawartość z ramki poniżej i wklej bezpośrednio do edytora WordPress (tryb HTML).
    </div>

    <div class="wordpress-content">
        <h4>👇 SKOPIUJ PONIŻSZĄ ZAWARTOŚĆ DO WORDPRESS:</h4>
        <pre>${titleHTML}
${content}</pre>
    </div>

    <div class="wordpress-note">
        <strong>✅ Ten artykuł jest zoptymalizowany pod:</strong><br>
        • 🔍 Google SEO (tradycyjne pozycjonowanie)<br>
        • 🤖 AI Search Engines (Google AI, ChatGPT, Perplexity)<br>
        • 📝 WordPress (czysty HTML bez CSS)<br>
        • 📱 Mobile-friendly struktura<br>
        • 🎯 Wysokie pozycjonowanie w TOP 10
    </div>

    <hr style="margin: 30px 0;">

    <h2>📖 PODGLĄD ARTYKUŁU:</h2>
    <article>
        ${titleHTML}
        ${content}
    </article>
</body>
</html>`;

                // Utwórz nazwę pliku
                const fileName = title.replace(/[^a-zA-Z0-9ąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s-]/g, '').replace(/\s+/g, '-').toLowerCase() + '.html';

                // Utwórz plik w folderze "artykuly"
                const fileHandle = await articlesDirectoryHandle.getFileHandle(fileName, {
                    create: true
                });

                // Zapisz zawartość do pliku
                const writable = await fileHandle.createWritable();
                await writable.write(htmlContent);
                await writable.close();

                console.log(`Artykuł zapisany w folderze artykuly: ${fileName}`);
                return true;

            } catch (error) {
                console.error('Błąd zapisywania do folderu artykuly:', error);
                throw error;
            }
        }

        // Funkcja do resetowania folderu artykułów
        function resetArticlesFolder() {
            articlesDirectoryHandle = null;
            alert('Folder docelowy został zresetowany. Przy następnym generowaniu zostaniesz poproszony o wybór nowego folderu.');
        }

        // Funkcja pomocnicza do tworzenia i pobierania pliku HTML
        function downloadHTMLFile(title, titleHTML, content) {
            // Wersja do importu do WordPress (czysty HTML)
            const wordpressContent = `<!-- ARTYKUŁ GOTOWY DO IMPORTU DO WORDPRESS -->
<!-- Skopiuj zawartość poniżej i wklej do edytora WordPress -->

${titleHTML}
${content}

<!-- KONIEC ARTYKUŁU -->`;

            // Wersja do podglądu (z podstawowym stylem)
            const htmlContent = `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; color: #333; }
        h1, h2, h3 { color: #2c3e50; margin-top: 30px; margin-bottom: 15px; }
        h1 { font-size: 2.2em; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { font-size: 1.8em; color: #34495e; }
        h3 { font-size: 1.4em; color: #7f8c8d; }
        p { margin-bottom: 15px; text-align: justify; }
        ul, ol { margin-bottom: 15px; padding-left: 30px; }
        li { margin-bottom: 8px; }
        strong { font-weight: bold; color: #2c3e50; }
        em { font-style: italic; color: #7f8c8d; }
        .wordpress-note { background: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="wordpress-note">
        <strong>📝 Instrukcja:</strong> Ten plik zawiera artykuł gotowy do WordPress.
        Skopiuj zawartość z komentarzy HTML lub użyj tego podglądu do sprawdzenia formatowania.
    </div>

    <article>
        ${titleHTML}
        ${content}
    </article>

    <div class="wordpress-note">
        <strong>✅ Artykuł zoptymalizowany pod:</strong><br>
        • Google SEO (tradycyjne pozycjonowanie)<br>
        • AI Search Engines (Google AI, ChatGPT, Perplexity)<br>
        • WordPress (czysty HTML bez CSS)<br>
        • Mobile-friendly struktura
    </div>
</body>
</html>`;
            
            const blob = new Blob([htmlContent], {type: 'text/html'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = title.replace(/\s+/g, '-').toLowerCase() + '.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // Funkcje integracji z WordPress
        
        // Połączenie z WordPress
        async function connectToWordPress() {
            if (!isLoggedIn) {
                alert('Musisz się zalogować, aby korzystać z tej funkcji');
                return;
            }
            
            const wpUrl = document.getElementById('wpUrl').value.trim();
            const wpUsername = document.getElementById('wpUsername').value.trim();
            const wpPassword = document.getElementById('wpPassword').value.trim();
            
            if (!wpUrl || !wpUsername || !wpPassword) {
                alert('Wprowadź wszystkie dane do połączenia z WordPress');
                return;
            }
            
            // Pokaż pole postępu generowania
            document.getElementById('generationProgressContainer').style.display = 'block';
            document.getElementById('generationProgress').innerHTML = '';
            updateGenerationProgress('Łączenie z WordPress...');
            
            try {
                // Sprawdź, czy URL kończy się slashem
                const baseUrl = wpUrl.endsWith('/') ? wpUrl : wpUrl + '/';
                const apiUrl = `${baseUrl}wp-json/wp/v2/categories`;
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Basic ' + btoa(wpUsername + ':' + wpPassword)
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`Błąd połączenia: ${response.status}`);
                }
                
                const categories = await response.json();
                
                if (categories.length === 0) {
                    alert('Nie znaleziono kategorii na stronie WordPress');
                    return;
                }
                
                // Zapisz kategorie
                wpCategories = categories;
                
                // Wypełnij listę kategorii
                const categorySelect = document.getElementById('wpCategory');
                categorySelect.innerHTML = '';
                
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
                
                // Pokaż sekcję kategorii
                document.getElementById('wpCategoriesContainer').style.display = 'block';
                
                updateGenerationProgress('Połączono z WordPress pomyślnie!');
                alert('Połączono z WordPress pomyślnie!');
                
                // Zapisz konfigurację
                saveConfig();
                
            } catch (error) {
                console.error('Błąd połączenia z WordPress:', error);
                alert(`Błąd połączenia z WordPress: ${error.message}`);
            }
        }
        
        // Wybór artykułów z komputera
        function selectArticlesFromComputer() {
            if (!isLoggedIn) {
                alert('Musisz się zalogować, aby korzystać z tej funkcji');
                return;
            }
            
            // Utwórz ukryty input typu file
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.accept = '.html';
            
            fileInput.addEventListener('change', function() {
                const files = Array.from(this.files);
                
                if (files.length === 0) return;
                
                // Zapisz wybrane pliki
                selectedArticleFiles = files;
                
                // Wyświetl listę plików
                const fileListContainer = document.getElementById('selectedFiles');
                fileListContainer.innerHTML = '';
                
                files.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span>${file.name}</span>
                        <button class="remove-btn" onclick="removeSelectedFile(${index})">Usuń</button>
                    `;
                    fileListContainer.appendChild(fileItem);
                });
            });
            
            // Kliknij w input
            fileInput.click();
        }
        
        // Usunięcie wybranego pliku
        function removeSelectedFile(index) {
            selectedArticleFiles.splice(index, 1);
            
            // Zaktualizuj listę plików
            const fileListContainer = document.getElementById('selectedFiles');
            
            if (selectedArticleFiles.length === 0) {
                fileListContainer.innerHTML = '<p>Wybrane pliki pojawią się tutaj</p>';
                return;
            }
            
            fileListContainer.innerHTML = '';
            selectedArticleFiles.forEach((file, idx) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name}</span>
                    <button class="remove-btn" onclick="removeSelectedFile(${idx})">Usuń</button>
                `;
                fileListContainer.appendChild(fileItem);
            });
        }
        
        // Zaplanowanie publikacji artykułów
        async function scheduleArticles() {
            if (!isLoggedIn) {
                alert('Musisz się zalogować, aby korzystać z tej funkcji');
                return;
            }
            
            if (selectedArticleFiles.length === 0) {
                alert('Wybierz pliki artykułów do publikacji');
                return;
            }
            
            const categoryId = document.getElementById('wpCategory').value;
            if (!categoryId) {
                alert('Wybierz kategorię dla artykułów');
                return;
            }
            
            const startDate = document.getElementById('publishStartDate').value;
            if (!startDate) {
                alert('Wybierz datę rozpoczęcia publikacji');
                return;
            }
            
            // Pokaż pole postępu generowania
            document.getElementById('generationProgressContainer').style.display = 'block';
            document.getElementById('generationProgress').innerHTML = '';
            updateGenerationProgress('Rozpoczynam planowanie publikacji artykułów...');
            
            const interval = parseInt(document.getElementById('publishInterval').value);
            const articlesPerDay = parseInt(document.getElementById('articlesPerDay').value);
            
            if (isNaN(interval) || interval < 0) {
                alert('Wprowadź poprawną wartość interwału publikacji (0 lub więcej)');
                return;
            }
            
            if (isNaN(articlesPerDay) || articlesPerDay < 1) {
                alert('Wprowadź poprawną liczbę artykułów dziennie (minimum 1)');
                return;
            }
            
            // Dane WordPress
            const wpUrl = document.getElementById('wpUrl').value.trim();
            const wpUsername = document.getElementById('wpUsername').value.trim();
            const wpPassword = document.getElementById('wpPassword').value.trim();

            if (!wpUrl || !wpUsername || !wpPassword) {
                alert('❌ Błąd!\n\nWypełnij wszystkie dane WordPress:\n• Adres strony\n• Nazwa użytkownika\n• Hasło API');
                return;
            }

            const baseUrl = wpUrl.endsWith('/') ? wpUrl : wpUrl + '/';
            const apiUrl = `${baseUrl}wp-json/wp/v2/posts`;

            // Sprawdź połączenie z WordPress przed publikacją
            updateGenerationProgress('🔍 Sprawdzanie połączenia z WordPress...');
            try {
                const testResponse = await fetch(`${baseUrl}wp-json/wp/v2/categories`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Basic ' + btoa(wpUsername + ':' + wpPassword)
                    }
                });

                if (!testResponse.ok) {
                    throw new Error(`Błąd połączenia z WordPress: ${testResponse.status} ${testResponse.statusText}`);
                }

                updateGenerationProgress('✅ Połączenie z WordPress działa poprawnie');
            } catch (error) {
                updateGenerationProgress(`❌ Błąd połączenia z WordPress: ${error.message}`);
                alert(`❌ Błąd połączenia z WordPress!\n\n${error.message}\n\nSprawdź:\n• Adres strony WordPress\n• Dane logowania\n• Uprawnienia użytkownika`);
                return;
            }

            try {
                // Przygotuj daty publikacji
                const dates = [];
                let currentDate = new Date(startDate);
                
                if (interval === 0) {
                    // Wszystkie artykuły w jednym dniu
                    const articlesCount = selectedArticleFiles.length;
                    
                    // Godziny publikacji od 9:00 do 18:00
                    const startHour = 9;
                    const endHour = 18;
                    const totalMinutes = (endHour - startHour) * 60;
                    
                    // Oblicz interwał czasowy między publikacjami
                    let timeInterval = Math.floor(totalMinutes / articlesCount);
                    
                    // Minimum 1 minuta między publikacjami
                    timeInterval = Math.max(1, timeInterval);
                    
                    for (let i = 0; i < articlesCount; i++) {
                        const publishDate = new Date(currentDate);
                        
                        // Ustaw godzinę publikacji
                        const minutesFromStart = i * timeInterval;
                        const hour = startHour + Math.floor(minutesFromStart / 60);
                        const minute = minutesFromStart % 60;
                        
                        publishDate.setHours(hour, minute, 0, 0);
                        
                        // Jeśli przekroczyliśmy 18:00, publikuj kilka w tym samym czasie
                        if (hour >= endHour) {
                            publishDate.setHours(endHour - 1, 59, 0, 0);
                        }
                        
                        dates.push(publishDate);
                    }
                } else {
                    // Publikacja co kilka dni
                    let dayCounter = 0;
                    let articleCounter = 0;
                    
                    for (let i = 0; i < selectedArticleFiles.length; i++) {
                        // Jeśli przekroczyliśmy limit artykułów na dzień, przejdź do następnego dnia
                        if (articleCounter >= articlesPerDay) {
                            articleCounter = 0;
                            dayCounter++;
                            currentDate = new Date(startDate);
                            currentDate.setDate(currentDate.getDate() + (dayCounter * interval));
                        }
                        
                        // Godziny publikacji od 9:00 do 18:00
                        const startHour = 9;
                        const endHour = 18;
                        const totalMinutes = (endHour - startHour) * 60;
                        
                        // Oblicz interwał czasowy między publikacjami w danym dniu
                        let timeInterval = Math.floor(totalMinutes / articlesPerDay);
                        
                        // Minimum 1 minuta między publikacjami
                        timeInterval = Math.max(1, timeInterval);
                        
                        const publishDate = new Date(currentDate);
                        
                        // Ustaw godzinę publikacji
                        const minutesFromStart = articleCounter * timeInterval;
                        const hour = startHour + Math.floor(minutesFromStart / 60);
                        const minute = minutesFromStart % 60;
                        
                        publishDate.setHours(hour, minute, 0, 0);
                        
                        dates.push(publishDate);
                        articleCounter++;
                    }
                }
                
                // Publikuj artykuły
                let successCount = 0;
                
                for (let i = 0; i < selectedArticleFiles.length; i++) {
                    const file = selectedArticleFiles[i];
                    const publishDate = dates[i];
                    
                    // Odczytaj zawartość pliku
                    const fileContent = await readFileContent(file);
                    
                    // Wyodrębnij tytuł i treść z pliku HTML
                    const { title, content } = extractArticleContent(fileContent);
                    
                    // Przygotuj dane posta
                    const postData = {
                        title: title,
                        content: content,
                        status: 'future',
                        categories: [categoryId],
                        date: publishDate.toISOString()
                    };
                    
                    // Wyślij post do WordPress
                    try {
                        console.log('Wysyłanie artykułu do WordPress:', postData);
                        console.log('URL API:', apiUrl);
                        console.log('Dane autoryzacji:', wpUsername, wpPassword ? '[HASŁO USTAWIONE]' : '[BRAK HASŁA]');

                        // Dodaj debugowanie
                        updateGenerationProgress(`📤 Wysyłanie artykułu "${title}" do WordPress (${i+1}/${selectedArticleFiles.length})...`);
                        
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Basic ' + btoa(wpUsername + ':' + wpPassword)
                            },
                            body: JSON.stringify(postData)
                        });
                        
                        // Sprawdź odpowiedź tekstową przed parsowaniem JSON
                        const responseText = await response.text();
                        console.log('Odpowiedź z WordPress:', responseText);
                        
                        let responseData;
                        try {
                            responseData = JSON.parse(responseText);
                        } catch (e) {
                            console.error('Błąd parsowania JSON:', e);
                            throw new Error('Nieprawidłowa odpowiedź z serwera WordPress');
                        }
                        
                        if (response.ok) {
                            successCount++;
                            console.log(`Artykuł "${title}" zaplanowany na ${publishDate.toLocaleString()}`);
                            updateGenerationProgress(`Artykuł "${title}" zaplanowany na ${publishDate.toLocaleString()}`);
                        } else {
                            console.error(`Błąd publikacji artykułu ${file.name}:`, responseData);
                            updateGenerationProgress(`Błąd publikacji artykułu ${file.name}: ${responseData.message || 'Nieznany błąd'}`);
                            // Usunięto alert
                        }
                    } catch (error) {
                        console.error(`Błąd wysyłania artykułu ${file.name}:`, error);
                        updateGenerationProgress(`Błąd wysyłania artykułu ${file.name}: ${error.message}`);
                        // Usunięto alert
                    }
                }
                
                // Pokaż końcowy komunikat
                setTimeout(() => {
                    if (successCount > 0) {
                        updateGenerationProgress(`✅ SUKCES: Zaplanowano publikację ${successCount} z ${selectedArticleFiles.length} artykułów w WordPress!`);
                        alert(`🎉 Sukces!\n\nZaplanowano publikację ${successCount} z ${selectedArticleFiles.length} artykułów.\n\nArtykuły będą publikowane zgodnie z harmonogramem w WordPress.`);

                        // Wyczyść wybrane pliki po udanej publikacji
                        selectedArticleFiles = [];
                        document.getElementById('selectedFiles').innerHTML = '<p>Wybrane pliki pojawią się tutaj</p>';
                    } else {
                        updateGenerationProgress('❌ BŁĄD: Nie udało się zaplanować publikacji żadnego artykułu.');
                        alert('❌ Błąd!\n\nNie udało się zaplanować publikacji żadnego artykułu.\n\nSprawdź:\n• Połączenie z WordPress\n• Uprawnienia użytkownika\n• Poprawność danych logowania');
                    }
                }, 1000);
                
            } catch (error) {
                console.error('Błąd planowania publikacji:', error);
                updateGenerationProgress(`Błąd planowania publikacji: ${error.message}`);
                // Usunięto alert
            }
        }
        
        // Funkcja do odczytu zawartości pliku
        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                
                reader.onload = function(event) {
                    resolve(event.target.result);
                };
                
                reader.onerror = function(error) {
                    reject(error);
                };
                
                reader.readAsText(file);
            });
        }
        
        // Funkcja do wyodrębniania tytułu i treści z pliku HTML
        function extractArticleContent(htmlContent) {
            // Utwórz tymczasowy element do parsowania HTML
            const tempElement = document.createElement('div');
            tempElement.innerHTML = htmlContent;
            
            // Znajdź tytuł (h1 lub h2)
            let titleElement = tempElement.querySelector('h1, h2');
            const title = titleElement ? titleElement.textContent.trim() : 'Artykuł bez tytułu';
            
            // Znajdź treść (article lub div.blog-content)
            let contentElement = tempElement.querySelector('.blog-content');
            if (!contentElement) {
                contentElement = tempElement.querySelector('article');
            }
            
            // Jeśli nie znaleziono elementu .blog-content ani article, użyj całej zawartości
            let content;
            if (contentElement) {
                content = contentElement.innerHTML;
            } else {
                // Spróbuj znaleźć treść w body
                const bodyContent = tempElement.querySelector('body');
                if (bodyContent) {
                    // Usuń wszystkie skrypty i style
                    const scripts = bodyContent.querySelectorAll('script, style');
                    scripts.forEach(script => script.remove());
                    
                    // Usuń nagłówek i stopkę, jeśli istnieją
                    const header = bodyContent.querySelector('header');
                    const footer = bodyContent.querySelector('footer');
                    if (header) footer.remove();
                    if (footer) footer.remove();
                    
                    content = bodyContent.innerHTML;
                } else {
                    // Jeśli nie ma body, użyj całej zawartości
                    content = htmlContent;
                }
            }
            
            // Usuń powielony tytuł z początku treści, jeśli istnieje
            // Użyj wyrażenia regularnego, aby dopasować tytuł na początku treści, ignorując tagi HTML
            const cleanContent = content.replace(new RegExp(`^\\s*<[^>]+>\\s*${escapeRegExp(title)}\\s*<[^>]+>\\s*`), '').trim();
            
            // Upewnij się, że treść jest poprawnym HTML
            content = sanitizeHtml(cleanContent); // Użyj wyczyszczonej treści
            
            console.log('Wyodrębniony tytuł:', title);
            console.log('Wyodrębniona treść (fragment):', content.substring(0, 100) + '...');
            
            return { title, content };
        }

        // Funkcja pomocnicza do escapowania znaków specjalnych w wyrażeniach regularnych
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& oznacza dopasowany podciąg
        }
        
        // Funkcja do czyszczenia HTML
        function sanitizeHtml(html) {
            // Utwórz niepotrzebne atrybuty, które mogą powodować problemy
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            
            // Usuń atrybuty style, które mogą kolidować z motywem WordPress
            const elementsWithStyle = tempDiv.querySelectorAll('[style]');
            elementsWithStyle.forEach(el => el.removeAttribute('style'));
            
            // Usuń klasy, które mogą kolidować z motywem WordPress
            const elementsWithClass = tempDiv.querySelectorAll('[class]');
            elementsWithClass.forEach(el => el.removeAttribute('class'));
            
            // Usuń identyfikatory, które mogą kolidować z motywem WordPress
            const elementsWithId = tempDiv.querySelectorAll('[id]');
            elementsWithId.forEach(el => el.removeAttribute('id'));
            
            return tempDiv.innerHTML;
        }
        
        // Funkcja zmiany języka
        function changeLanguage(lang) {
            // Zmień atrybut lang w HTML
            document.getElementById('htmlRoot').lang = lang;
            
            // Zapisz preferencję języka
            localStorage.setItem('preferred_language', lang);
            
            // Zaktualizuj teksty na stronie
            updateUITexts(lang);
        }
        
        // Funkcja aktualizująca teksty interfejsu
        function updateUITexts(lang) {
            const translations = {
                'pl': {
                    // Logowanie
                    'loginTitle': 'Logowanie',
                    'loginUsername': 'Login:',
                    'loginPassword': 'Hasło:',
                    'loginButton': 'Zaloguj się',
                    'loginError': 'Nieprawidłowy login lub hasło',
                    
                    // Nawigacja
                    'generatorTab': 'Generator',
                    'wordpressTab': 'WordPress',
                    
                    // Tytuły
                    'addTitle': 'Dodaj tytuł artykułu:',
                    'addButton': 'Dodaj',
                    'bulkTitles': 'Masowe dodawanie tytułów (jeden pod drugim lub oddzielone średnikami):',
                    'addBulkButton': 'Dodaj tytuły masowo',
                    'clearAllTitles': '🗑️ Usuń wszystkie tytuły',
                    'generateTitlesLabel': 'Generowanie tytułów przez AI:',
                    'generateTitlesButton': 'Wygeneruj tytuły',
                    'noTitles': 'Dodane tytuły pojawią się tutaj',
                    'editButton': 'Edytuj',
                    'removeButton': 'Usuń',
                    
                    // API
                    'apiProvider': 'Dostawca API:',
                    'saveConfig': 'Zapisz konfigurację',
                    'loadConfig': 'Wczytaj konfigurację',
                    'downloadConfig': 'Pobierz konfigurację',
                    'uploadConfig': 'Załaduj konfigurację',
                    'openaiKey': 'Klucz API OpenAI:',
                    'saveKey': 'Zapisz klucz',
                    'keyInfo': 'Klucz jest zapisywany tylko w Twojej przeglądarce i nie jest wysyłany nigdzie poza API.',
                    'openaiModel': 'Model OpenAI:',
                    'openrouterKey': 'Klucz API OpenRouter:',
                    'openrouterModel': 'Model OpenRouter:',
                    
                    // Ustawienia artykułu
                    'additionalInfo': 'Informacje dodatkowe (zostaną dodane na końcu artykułu):',
                    'targetKeywords': '🎯 Słowa kluczowe SEO (opcjonalne):',
                    'length': 'Długość artykułu:',
                    'short': 'Krótki (min. 400 słów)',
                    'medium': 'Średni (min. 700 słów)',
                    'long': 'Długi (min. 1200 słów)',
                    'tone': 'Ton artykułu:',
                    'professional': 'Profesjonalny',
                    'casual': 'Przyjazny/konwersacyjny',
                    'expert': 'Ekspercki',
                    'generateButton': 'Generuj artykuły',
                    'resetFolderBtn': '📁 Zmień folder',
                    
                    // WordPress
                    'wpIntegration': 'Integracja z WordPress',
                    'wpUrl': 'Adres strony WordPress:',
                    'wpUsername': 'Nazwa użytkownika:',
                    'wpPassword': 'Hasło API:',
                    'wpConnect': 'Połącz z WordPress',
                    'wpCategory': 'Wybierz kategorię:',
                    'wpSelectArticles': 'Wybierz artykuły do publikacji:',
                    'wpSelectFiles': 'Wybierz pliki HTML',
                    'wpNoFiles': 'Wybrane pliki pojawią się tutaj',
                    'wpSchedule': 'Harmonogram publikacji:',
                    'wpStartDate': 'Data rozpoczęcia:',
                    'wpInterval': 'Publikuj co (dni):',
                    'wpArticlesPerDay': 'Artykułów dziennie:',
                    'wpIntervalHint': 'Wartość 0 w polu "Publikuj co" oznacza natychmiastową publikację wszystkich artykułów.',
                    'wpScheduleButton': 'Zaplanuj publikację artykułów'
                },
                'en': {
                    // Login
                    'loginTitle': 'Login',
                    'loginUsername': 'Username:',
                    'loginPassword': 'Password:',
                    'loginButton': 'Log in',
                    'loginError': 'Invalid username or password',
                    
                    // Navigation
                    'generatorTab': 'Generator',
                    'wordpressTab': 'WordPress',
                    
                    // Tytuły
                    'addTitle': 'Add article title:',
                    'addButton': 'Add',
                    'bulkTitles': 'Bulk add titles (one per line or semicolon separated):',
                    'addBulkButton': 'Add titles in bulk',
                    'clearAllTitles': '🗑️ Clear all titles',
                    'generateTitlesLabel': 'Generate titles with AI:',
                    'generateTitlesButton': 'Generate titles',
                    'noTitles': 'Added titles will appear here',
                    'editButton': 'Edit',
                    'removeButton': 'Remove',
                    
                    // API
                    'apiProvider': 'API Provider:',
                    'saveConfig': 'Save configuration',
                    'loadConfig': 'Load configuration',
                    'downloadConfig': 'Download configuration',
                    'uploadConfig': 'Upload configuration',
                    'openaiKey': 'OpenAI API Key:',
                    'saveKey': 'Save key',
                    'keyInfo': 'The key is stored only in your browser and is not sent anywhere except to the API.',
                    'openaiModel': 'OpenAI Model:',
                    'openrouterKey': 'OpenRouter API Key:',
                    'openrouterModel': 'OpenRouter Model:',
                    
                    // Article settings
                    'additionalInfo': 'Additional information (will be added at the end of article):',
                    'targetKeywords': '🎯 SEO Keywords (optional):',
                    'length': 'Article length:',
                    'short': 'Short (min. 400 words)',
                    'medium': 'Medium (min. 700 words)',
                    'long': 'Long (min. 1200 words)',
                    'tone': 'Article tone:',
                    'professional': 'Professional',
                    'casual': 'Friendly/conversational',
                    'expert': 'Expert',
                    'generateButton': 'Generate articles',
                    'resetFolderBtn': '📁 Change Folder',
                    
                    // WordPress
                    'wpIntegration': 'WordPress Integration',
                    'wpUrl': 'WordPress site URL:',
                    'wpUsername': 'Username:',
                    'wpPassword': 'API Password:',
                    'wpConnect': 'Connect to WordPress',
                    'wpCategory': 'Select category:',
                    'wpSelectArticles': 'Select articles for publication:',
                    'wpSelectFiles': 'Select HTML files',
                    'wpNoFiles': 'Selected files will appear here',
                    'wpSchedule': 'Publication schedule:',
                    'wpStartDate': 'Start date:',
                    'wpInterval': 'Publish every (days):',
                    'wpArticlesPerDay': 'Articles per day:',
                    'wpIntervalHint': 'Value 0 in "Publish every" field means immediate publication of all articles.',
                    'wpScheduleButton': 'Schedule article publication'
                }
            };
            
            const texts = translations[lang] || translations['pl'];
            
            // Aktualizacja tekstów w interfejsie
            // Logowanie
            document.querySelector('.login-container h2').textContent = texts.loginTitle;
            document.querySelector('label[for="username"]').textContent = texts.loginUsername;
            document.querySelector('label[for="password"]').textContent = texts.loginPassword;
            document.querySelector('.login-container .btn').textContent = texts.loginButton;
            document.getElementById('loginError').textContent = texts.loginError;
            
            // Nawigacja
            document.getElementById('generatorTabBtn').textContent = texts.generatorTab;
            document.getElementById('wordpressTabBtn').textContent = texts.wordpressTab;
            
            // Tytuły
            document.querySelector('label[for="title"]').textContent = texts.addTitle;
            document.querySelector('button[onclick="addTitle()"]').textContent = texts.addButton;
            document.querySelector('label[for="bulkTitles"]').textContent = texts.bulkTitles;
            document.querySelector('button[onclick="addBulkTitles()"]').textContent = texts.addBulkButton;
            document.querySelector('button[onclick="clearAllTitles()"]').textContent = texts.clearAllTitles;
            document.querySelector('label[for="titleGenerationPrompt"]').textContent = texts.generateTitlesLabel;
            document.querySelector('button[onclick="generateTitles()"]').textContent = texts.generateTitlesButton;
            
            if (titles.length === 0) {
                document.getElementById('titlesList').innerHTML = `<p>${texts.noTitles}</p>`;
            }
            
            // API
            document.querySelector('label[for="apiProvider"]').textContent = texts.apiProvider;
            document.querySelector('button[onclick="saveConfig()"]').textContent = texts.saveConfig;
            document.querySelector('button[onclick="loadConfig()"]').textContent = texts.loadConfig;
            document.querySelector('button[onclick="downloadConfigFile()"]').textContent = texts.downloadConfig;
            document.querySelector('button[onclick="uploadConfigFile()"]').textContent = texts.uploadConfig;
            
            document.querySelector('label[for="apiKey"]').textContent = texts.openaiKey;
            document.querySelector('button[onclick="setApiKey(document.getElementById(\'apiKey\').value, \'openai\')"]').textContent = texts.saveKey;
            document.querySelector('#openaiKeyGroup small').textContent = texts.keyInfo;
            
            document.querySelector('label[for="openaiModel"]').textContent = texts.openaiModel;
            
            document.querySelector('label[for="openrouterApiKey"]').textContent = texts.openrouterKey;
            document.querySelector('button[onclick="setApiKey(document.getElementById(\'openrouterApiKey\').value, \'openrouter\')"]').textContent = texts.saveKey;
            document.querySelector('#openrouterKeyGroup small').textContent = texts.keyInfo;
            
            document.querySelector('label[for="openrouterModel"]').textContent = texts.openrouterModel;
            
            // Ustawienia artykułu
            document.querySelector('label[for="additionalInfo"]').textContent = texts.additionalInfo;
            document.querySelector('label[for="targetKeywords"]').textContent = texts.targetKeywords;
            document.querySelector('label[for="length"]').textContent = texts.length;
            document.querySelector('#length option[value="short"]').textContent = texts.short;
            document.querySelector('#length option[value="medium"]').textContent = texts.medium;
            document.querySelector('#length option[value="long"]').textContent = texts.long;
            
            document.querySelector('label[for="tone"]').textContent = texts.tone;
            document.querySelector('#tone option[value="professional"]').textContent = texts.professional;
            document.querySelector('#tone option[value="casual"]').textContent = texts.casual;
            document.querySelector('#tone option[value="expert"]').textContent = texts.expert;
            
            document.getElementById('generateBtn').textContent = texts.generateButton;
            document.getElementById('resetFolderBtn').textContent = texts.resetFolderBtn;
            
            // WordPress
            document.querySelector('.wordpress-integration h2').textContent = texts.wpIntegration;
            document.getElementById('wpUrlLabel').textContent = texts.wpUrl;
            document.getElementById('wpUsernameLabel').textContent = texts.wpUsername;
            document.getElementById('wpPasswordLabel').textContent = texts.wpPassword;
            document.getElementById('wpConnectBtn').textContent = texts.wpConnect;
            document.getElementById('wpCategoryLabel').textContent = texts.wpCategory;
            document.getElementById('wpSelectArticlesLabel').textContent = texts.wpSelectArticles;
            document.getElementById('wpSelectFilesBtn').textContent = texts.wpSelectFiles;
            document.getElementById('wpNoFilesText').textContent = texts.wpNoFiles;
            document.getElementById('wpScheduleLabel').textContent = texts.wpSchedule;
            document.getElementById('wpStartDateLabel').textContent = texts.wpStartDate;
            document.getElementById('wpIntervalLabel').textContent = texts.wpInterval;
            document.getElementById('wpArticlesPerDayLabel').textContent = texts.wpArticlesPerDay;
            document.getElementById('wpIntervalHint').textContent = texts.wpIntervalHint;
            document.getElementById('wpScheduleBtn').textContent = texts.wpScheduleButton;
        }
        
        // Funkcja do przełączania widoczności pola hasła
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            if (input.type === "password") {
                input.type = "text";
            } else {
                input.type = "password";
            }
        }
        
        // Funkcja przełączania zakładek
        function showTab(tabName) {
            // Ukryj wszystkie zakładki
            document.getElementById('generatorTab').style.display = 'none';
            document.getElementById('wordpressTab').style.display = 'none';
            
            // Zresetuj style przycisków
            document.getElementById('generatorTabBtn').style.background = '#6366f1';
            document.getElementById('wordpressTabBtn').style.background = '#6366f1';
            
            // Pokaż wybraną zakładkę i podświetl przycisk
            if (tabName === 'generator') {
                document.getElementById('generatorTab').style.display = 'block';
                document.getElementById('generatorTabBtn').style.background = '#4f46e5';
            } else if (tabName === 'wordpress') {
                document.getElementById('wordpressTab').style.display = 'block';
                document.getElementById('wordpressTabBtn').style.background = '#4f46e5';
            }
        }
        
        // Funkcja do pobierania i wypełniania modeli OpenRouter
        async function fetchAndPopulateOpenRouterModels() {
            const openrouterModelSelect = document.getElementById('openrouterModel');
            openrouterModelSelect.innerHTML = '<option value="">Ładowanie modeli...</option>'; // Wyczyść i pokaż status ładowania
            
            if (!openRouterApiKey) {
                openrouterModelSelect.innerHTML = '<option value="">Brak klucza API OpenRouter</option>';
                return;
            }

            try {
                const response = await fetch('https://openrouter.ai/api/v1/models', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${openRouterApiKey.trim()}`
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Błąd API OpenRouter: ${errorData.error?.message || response.status}`);
                }

                const data = await response.json();
                
                openrouterModelSelect.innerHTML = ''; // Wyczyść istniejące opcje

                // Dodaj opcję domyślną
                const defaultOption = document.createElement('option');
                defaultOption.value = "";
                defaultOption.textContent = "Wybierz model OpenRouter";
                openrouterModelSelect.appendChild(defaultOption);

                data.data.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    // Sprawdź, czy model jest darmowy i dodaj informację
                    if (model.pricing && model.pricing.prompt === 0 && model.pricing.completion === 0) {
                        option.textContent += ' (free)';
                    }
                    openrouterModelSelect.appendChild(option);
                });

                // Ustaw wcześniej wybrany model, jeśli istnieje
                const savedOpenrouterModel = localStorage.getItem('openrouter_model');
                if (savedOpenrouterModel) {
                    openrouterModelSelect.value = savedOpenrouterModel;
                }

                updateGenerationProgress('Modele OpenRouter załadowane pomyślnie.');

            } catch (error) {
                console.error('Błąd ładowania modeli OpenRouter:', error);
                openrouterModelSelect.innerHTML = `<option value="">Błąd ładowania: ${error.message}</option>`;
                updateGenerationProgress(`Błąd ładowania modeli OpenRouter: ${error.message}`);
            }
        }

        // Zapisanie konfiguracji WordPress do localStorage
        function saveWordPressConfig() {
            const wpConfig = {
                url: document.getElementById('wpUrl').value,
                username: document.getElementById('wpUsername').value,
                password: document.getElementById('wpPassword').value
            };
            localStorage.setItem('wordpress_config', JSON.stringify(wpConfig));
            showConfigMessage('Konfiguracja WordPress zapisana lokalnie', 'success');
        }

        // Wczytanie konfiguracji WordPress z localStorage
        function loadWordPressConfig() {
            const wpConfigStr = localStorage.getItem('wordpress_config');
            if (wpConfigStr) {
                const wpConfig = JSON.parse(wpConfigStr);
                document.getElementById('wpUrl').value = wpConfig.url || '';
                document.getElementById('wpUsername').value = wpConfig.username || '';
                document.getElementById('wpPassword').value = wpConfig.password || '';
                showConfigMessage('Konfiguracja WordPress wczytana lokalnie', 'success');
            } else {
                showConfigMessage('Brak zapisanej konfiguracji WordPress', 'warning');
            }
        }

        // Pobieranie konfiguracji WordPress jako plik
        function downloadWordPressConfigFile() {
            const wpConfig = {
                url: document.getElementById('wpUrl').value,
                username: document.getElementById('wpUsername').value,
                password: document.getElementById('wpPassword').value
            };
            const configStr = JSON.stringify(wpConfig, null, 2);
            const blob = new Blob([configStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'wordpress_config.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showConfigMessage('Plik konfiguracyjny WordPress został pobrany', 'success');
        }

        // Wczytywanie konfiguracji WordPress z pliku
        function uploadWordPressConfigFile() {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.json';
            
            fileInput.addEventListener('change', async function() {
                if (this.files && this.files[0]) {
                    try {
                        const file = this.files[0];
                        const fileContent = await readFileContent(file);
                        const wpConfig = JSON.parse(fileContent);
                        
                        document.getElementById('wpUrl').value = wpConfig.url || '';
                        document.getElementById('wpUsername').value = wpConfig.username || '';
                        document.getElementById('wpPassword').value = wpConfig.password || '';
                        
                        localStorage.setItem('wordpress_config', JSON.stringify(wpConfig));
                        
                        showConfigMessage('Konfiguracja WordPress załadowana z pliku pomyślnie', 'success');
                    } catch (error) {
                        console.error('Błąd wczytywania pliku konfiguracyjnego WordPress:', error);
                        showConfigMessage(`Błąd wczytywania pliku konfiguracyjnego WordPress: ${error.message}`, 'error');
                    }
                }
            });
            
            fileInput.click();
        }
        
        // Załadowanie konfiguracji przy uruchomieniu
        document.addEventListener('DOMContentLoaded', function() {
            // Ustaw OpenRouter jako domyślny i pokaż odpowiednie pola
            currentProvider = 'openrouter';
            toggleApiProviderFields();

            // Nie ładujemy konfiguracji tutaj - zostanie załadowana po zalogowaniu
            
            // Zapisz wybrany dostawcę przy zmianie
            document.getElementById('apiProvider').addEventListener('change', function() {
                currentProvider = this.value;
                toggleApiProviderFields(); // Pokaż odpowiednie pola i załaduj modele jeśli trzeba
                saveConfig();
            });

            // Automatyczne ładowanie modeli OpenRouter po wprowadzeniu klucza API
            document.getElementById('openrouterApiKey').addEventListener('input', function() {
                const apiKey = this.value.trim();
                if (apiKey && apiKey.length > 10) { // Sprawdź czy klucz ma sensowną długość
                    // Opóźnij wywołanie, żeby nie robić zbyt wielu zapytań podczas pisania
                    clearTimeout(window.openrouterKeyTimeout);
                    window.openrouterKeyTimeout = setTimeout(() => {
                        openRouterApiKey = apiKey;
                        if (currentProvider === 'openrouter') {
                            fetchAndPopulateOpenRouterModels();
                        }
                    }, 1000); // Poczekaj 1 sekundę po ostatniej zmianie
                }
            });

            // Ustaw dzisiejszą datę jako domyślną datę rozpoczęcia publikacji
            const today = new Date();
            const formattedDate = today.toISOString().split('T')[0];
            document.getElementById('publishStartDate').value = formattedDate;
            
            // Załaduj preferowany język
            const preferredLanguage = localStorage.getItem('preferred_language') || 'pl';
            changeLanguage(preferredLanguage);
            
            // Domyślnie pokaż zakładkę generatora
            showTab('generator');
        });
    </script>
</body>
</html>
