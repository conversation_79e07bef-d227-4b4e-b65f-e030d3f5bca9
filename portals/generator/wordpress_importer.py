#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import glob
import json
from datetime import datetime, timedelta
from colorama import Fore, Style, init
from typing import List, Dict, Optional

from wordpress_client import WordPressClient
from utils import (
    Logger, clear_screen, print_header, get_user_input, 
    confirm_action, format_time_duration
)

# Inicjalizuj colorama
init(autoreset=True)

class WordPressImporter:
    """Importer artykułów do WordPress"""
    
    def __init__(self):
        self.logger = Logger("wordpress_import.log")
        self.wp_client = None
        self.config_file = "wordpress_config.json"
    
    def print_wp_header(self):
        """Wyświetla nagłówek importera WordPress"""
        header = f"""
{Fore.BLUE}╔══════════════════════════════════════════════════════════════╗
║                    IMPORTER WORDPRESS                        ║
║                                                              ║
║  📝 Import artykułów HTML do WordPress                      ║
║  📅 Planowanie publikacji                                   ║
║  🎯 Automatyczne kategoryzowanie                            ║
║  ⏰ Harmonogram publikacji                                  ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
        print(header)
    
    def load_wp_config(self) -> Optional[Dict]:
        """Ładuje konfigurację WordPress"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"{Fore.GREEN}✓ Wczytano konfigurację WordPress{Style.RESET_ALL}")
                return config
            except Exception as e:
                print(f"{Fore.RED}✗ Błąd wczytywania konfiguracji: {e}{Style.RESET_ALL}")
        return None
    
    def save_wp_config(self, config: Dict) -> bool:
        """Zapisuje konfigurację WordPress"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"{Fore.GREEN}✓ Zapisano konfigurację WordPress{Style.RESET_ALL}")
            return True
        except Exception as e:
            print(f"{Fore.RED}✗ Błąd zapisu konfiguracji: {e}{Style.RESET_ALL}")
            return False
    
    def setup_wordpress_connection(self) -> bool:
        """Konfiguruje połączenie z WordPress"""
        print(f"\n{Fore.CYAN}🔧 KONFIGURACJA WORDPRESS{Style.RESET_ALL}")
        
        # Spróbuj wczytać istniejącą konfigurację
        config = self.load_wp_config()
        
        if config:
            print(f"{Fore.BLUE}Obecna konfiguracja:{Style.RESET_ALL}")
            print(f"  URL: {config.get('url', 'Brak')}")
            print(f"  Użytkownik: {config.get('username', 'Brak')}")
            print(f"  Hasło: {'***' if config.get('password') else 'Brak'}")
            
            if confirm_action("Czy użyć istniejącej konfiguracji?"):
                url = config.get('url')
                username = config.get('username')
                password = config.get('password')
            else:
                url = username = password = None
        else:
            url = username = password = None
        
        # Pobierz dane od użytkownika jeśli potrzeba
        if not url:
            url = get_user_input("URL strony WordPress (np. https://twoja-strona.pl): ")
        
        if not username:
            username = get_user_input("Nazwa użytkownika WordPress: ")
        
        if not password:
            password = get_user_input("Hasło aplikacji WordPress: ")
        
        # Testuj połączenie
        print(f"\n{Fore.CYAN}🔄 Testowanie połączenia...{Style.RESET_ALL}")
        
        self.wp_client = WordPressClient(url, username, password)
        
        if self.wp_client.test_connection():
            # Zapisz konfigurację
            config = {
                "url": url,
                "username": username,
                "password": password,
                "last_used": datetime.now().isoformat()
            }
            self.save_wp_config(config)
            return True
        else:
            print(f"{Fore.RED}✗ Nie można połączyć się z WordPress{Style.RESET_ALL}")
            return False
    
    def select_category(self) -> Optional[int]:
        """Pozwala użytkownikowi wybrać kategorię"""
        print(f"\n{Fore.CYAN}📂 WYBÓR KATEGORII{Style.RESET_ALL}")
        
        categories = self.wp_client.get_categories()
        
        if not categories:
            print(f"{Fore.RED}✗ Nie znaleziono kategorii{Style.RESET_ALL}")
            return None
        
        print(f"\n{Fore.GREEN}Dostępne kategorie:{Style.RESET_ALL}")
        for i, category in enumerate(categories, 1):
            name = category.get('name', 'Bez nazwy')
            count = category.get('count', 0)
            print(f"{i:2d}. {name} ({count} postów)")
        
        while True:
            choice = get_user_input("Wybierz numer kategorii: ", input_type=int)
            
            if 1 <= choice <= len(categories):
                selected = categories[choice - 1]
                category_name = selected.get('name', 'Nieznana')
                category_id = selected.get('id')
                
                print(f"{Fore.GREEN}✓ Wybrano kategorię: {category_name} (ID: {category_id}){Style.RESET_ALL}")
                return category_id
            else:
                print(f"{Fore.RED}✗ Nieprawidłowy wybór! Wybierz 1-{len(categories)}{Style.RESET_ALL}")
    
    def select_html_files(self, folder: str = "artykuly") -> List[str]:
        """Pozwala użytkownikowi wybrać pliki HTML"""
        print(f"\n{Fore.CYAN}📄 WYBÓR PLIKÓW HTML{Style.RESET_ALL}")
        
        # Znajdź pliki HTML
        pattern = os.path.join(folder, "**/*.html")
        html_files = glob.glob(pattern, recursive=True)
        
        if not html_files:
            print(f"{Fore.RED}✗ Nie znaleziono plików HTML w folderze: {folder}{Style.RESET_ALL}")
            return []
        
        print(f"\n{Fore.GREEN}Znalezione pliki HTML ({len(html_files)}):{Style.RESET_ALL}")
        for i, file_path in enumerate(html_files, 1):
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"{i:2d}. {file_name} ({file_size} bajtów)")
        
        print(f"\n{Fore.YELLOW}Opcje wyboru:{Style.RESET_ALL}")
        print("1. Wszystkie pliki")
        print("2. Wybierz konkretne pliki (numery oddzielone przecinkami)")
        print("3. Ostatnie N plików")
        
        choice = get_user_input("Wybierz opcję (1-3): ")
        
        if choice == "1":
            return html_files
        elif choice == "2":
            indices_str = get_user_input("Podaj numery plików (np. 1,3,5-8): ")
            selected_files = []
            
            for part in indices_str.split(','):
                part = part.strip()
                if '-' in part:
                    # Zakres
                    start, end = map(int, part.split('-'))
                    for i in range(start, end + 1):
                        if 1 <= i <= len(html_files):
                            selected_files.append(html_files[i - 1])
                else:
                    # Pojedynczy numer
                    i = int(part)
                    if 1 <= i <= len(html_files):
                        selected_files.append(html_files[i - 1])
            
            return selected_files
        elif choice == "3":
            count = get_user_input("Ile ostatnich plików? ", input_type=int)
            return html_files[-count:] if count > 0 else []
        
        return []
    
    def configure_publishing_schedule(self) -> tuple[int, datetime]:
        """Konfiguruje harmonogram publikacji"""
        print(f"\n{Fore.CYAN}📅 HARMONOGRAM PUBLIKACJI{Style.RESET_ALL}")
        
        print(f"{Fore.YELLOW}Opcje publikacji:{Style.RESET_ALL}")
        print("1. Natychmiastowa publikacja wszystkich artykułów")
        print("2. Zaplanowana publikacja z interwałem")
        
        choice = get_user_input("Wybierz opcję (1-2): ")
        
        if choice == "1":
            return 0, datetime.now()
        else:
            days_interval = get_user_input("Co ile dni publikować artykuły? ", input_type=int)
            
            print(f"\n{Fore.YELLOW}Data rozpoczęcia:{Style.RESET_ALL}")
            print("1. Teraz")
            print("2. Określona data")
            
            start_choice = get_user_input("Wybierz opcję (1-2): ")
            
            if start_choice == "1":
                start_date = datetime.now()
            else:
                date_str = get_user_input("Podaj datę (YYYY-MM-DD HH:MM) lub (YYYY-MM-DD): ")
                try:
                    if len(date_str) == 10:  # YYYY-MM-DD
                        start_date = datetime.strptime(date_str, "%Y-%m-%d")
                    else:  # YYYY-MM-DD HH:MM
                        start_date = datetime.strptime(date_str, "%Y-%m-%d %H:%M")
                except ValueError:
                    print(f"{Fore.YELLOW}⚠ Nieprawidłowy format daty, używam obecnej{Style.RESET_ALL}")
                    start_date = datetime.now()
            
            return days_interval, start_date
    
    def run_import(self):
        """Uruchamia proces importu"""
        clear_screen()
        self.print_wp_header()
        
        try:
            # Konfiguracja WordPress
            if not self.setup_wordpress_connection():
                return
            
            # Wybór kategorii
            category_id = self.select_category()
            if not category_id:
                return
            
            # Wybór plików
            html_files = self.select_html_files()
            if not html_files:
                print(f"{Fore.RED}✗ Nie wybrano żadnych plików{Style.RESET_ALL}")
                return
            
            # Konfiguracja harmonogramu
            days_interval, start_date = self.configure_publishing_schedule()
            
            # Podsumowanie
            print(f"\n{Fore.GREEN}📋 PODSUMOWANIE IMPORTU{Style.RESET_ALL}")
            print(f"📄 Plików do importu: {len(html_files)}")
            print(f"📂 Kategoria: ID {category_id}")
            if days_interval == 0:
                print(f"🚀 Publikacja: Natychmiastowa")
            else:
                print(f"📅 Publikacja: Co {days_interval} dni od {start_date.strftime('%Y-%m-%d %H:%M')}")
            
            if not confirm_action("Czy rozpocząć import?"):
                print(f"{Fore.YELLOW}Import anulowany{Style.RESET_ALL}")
                return
            
            # Wykonaj import
            print(f"\n{Fore.CYAN}🚀 ROZPOCZYNAM IMPORT{Style.RESET_ALL}")
            start_time = datetime.now()
            
            results = self.wp_client.schedule_posts_from_files(
                html_files, category_id, days_interval, start_date
            )
            
            # Podsumowanie wyników
            end_time = datetime.now()
            duration = int((end_time - start_time).total_seconds())
            
            print(f"\n{Fore.GREEN}{'='*60}")
            print(f"                    PODSUMOWANIE IMPORTU")
            print(f"{'='*60}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}📊 Statystyki:{Style.RESET_ALL}")
            print(f"   ✅ Zaimportowane: {Fore.GREEN}{results['success']}{Style.RESET_ALL}")
            print(f"   ❌ Nieudane: {Fore.RED}{results['failed']}{Style.RESET_ALL}")
            print(f"   📄 Łącznie: {Fore.BLUE}{results['total']}{Style.RESET_ALL}")
            print(f"   ⏱️  Czas trwania: {Fore.YELLOW}{format_time_duration(duration)}{Style.RESET_ALL}")
            
            if results['success'] > 0:
                print(f"\n{Fore.GREEN}🎉 Import zakończony sukcesem!{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}❌ Import nie powiódł się{Style.RESET_ALL}")
            
            self.logger.info(f"Import zakończony. Sukces: {results['success']}, Błędy: {results['failed']}")
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Import przerwany przez użytkownika{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}❌ Błąd krytyczny: {e}{Style.RESET_ALL}")
            self.logger.error(f"Błąd krytyczny importu: {e}")

def main():
    """Główna funkcja"""
    importer = WordPressImporter()
    importer.run_import()

if __name__ == "__main__":
    main()
