#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
from datetime import datetime
from colorama import Fore, Style, init
from typing import Optional

from wordpress_client import WordPressClient
from wordpress_importer import WordPressImporter
from utils import (
    Logger, clear_screen, get_user_input, confirm_action
)

# Inicjalizuj colorama
init(autoreset=True)

class WordPressManager:
    """Menedżer WordPress - główne menu zarządzania"""
    
    def __init__(self):
        self.logger = Logger("wordpress_manager.log")
        self.config_file = "wordpress_config.json"
        self.wp_client = None
    
    def print_header(self):
        """Wyświetla nagłówek menedżera"""
        header = f"""
{Fore.BLUE}╔══════════════════════════════════════════════════════════════╗
║                   MENEDŻER WORDPRESS                         ║
║                                                              ║
║  📝 Zarządzanie artykułami WordPress                        ║
║  📤 Import artykułów HTML                                   ║
║  📅 Planowanie publikacji                                   ║
║  🔧 Konfiguracja połączenia                                 ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
        print(header)
    
    def load_config(self) -> Optional[dict]:
        """Ładuje konfigurację WordPress"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"{Fore.RED}✗ Błąd wczytywania konfiguracji: {e}{Style.RESET_ALL}")
        return None
    
    def show_connection_status(self):
        """Wyświetla status połączenia"""
        config = self.load_config()
        
        if config:
            print(f"{Fore.CYAN}🔗 Status połączenia WordPress:{Style.RESET_ALL}")
            print(f"   URL: {config.get('url', 'Brak')}")
            print(f"   Użytkownik: {config.get('username', 'Brak')}")
            print(f"   Ostatnie użycie: {config.get('last_used', 'Nigdy')}")
            
            # Test połączenia jeśli są dane
            if config.get('url') and config.get('username') and config.get('password'):
                try:
                    client = WordPressClient(
                        config['url'], 
                        config['username'], 
                        config['password']
                    )
                    if client.test_connection():
                        print(f"   Status: {Fore.GREEN}✓ Połączenie działa{Style.RESET_ALL}")
                    else:
                        print(f"   Status: {Fore.RED}✗ Błąd połączenia{Style.RESET_ALL}")
                except Exception as e:
                    print(f"   Status: {Fore.RED}✗ Błąd: {e}{Style.RESET_ALL}")
            else:
                print(f"   Status: {Fore.YELLOW}⚠ Niekompletna konfiguracja{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}⚠ Brak konfiguracji WordPress{Style.RESET_ALL}")
    
    def configure_wordpress(self):
        """Konfiguruje połączenie WordPress"""
        print(f"\n{Fore.CYAN}🔧 KONFIGURACJA WORDPRESS{Style.RESET_ALL}")
        
        config = self.load_config()
        
        # Pobierz dane
        if config:
            url = get_user_input(f"URL WordPress [{config.get('url', '')}]: ") or config.get('url', '')
            username = get_user_input(f"Użytkownik [{config.get('username', '')}]: ") or config.get('username', '')
            password = get_user_input("Hasło (Enter = bez zmiany): ") or config.get('password', '')
        else:
            url = get_user_input("URL WordPress: ")
            username = get_user_input("Użytkownik: ")
            password = get_user_input("Hasło: ")
        
        # Test połączenia
        print(f"\n{Fore.CYAN}🔄 Testowanie połączenia...{Style.RESET_ALL}")
        
        try:
            client = WordPressClient(url, username, password)
            if client.test_connection():
                # Zapisz konfigurację
                new_config = {
                    "url": url,
                    "username": username,
                    "password": password,
                    "last_used": datetime.now().isoformat()
                }
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(new_config, f, indent=2, ensure_ascii=False)
                
                print(f"{Fore.GREEN}✓ Konfiguracja zapisana pomyślnie{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}✗ Nie można połączyć się z WordPress{Style.RESET_ALL}")
        
        except Exception as e:
            print(f"{Fore.RED}✗ Błąd konfiguracji: {e}{Style.RESET_ALL}")
    
    def quick_import(self):
        """Szybki import artykułów"""
        print(f"\n{Fore.CYAN}🚀 SZYBKI IMPORT ARTYKUŁÓW{Style.RESET_ALL}")
        
        # Sprawdź konfigurację
        config = self.load_config()
        if not config or not all(config.get(k) for k in ['url', 'username', 'password']):
            print(f"{Fore.RED}✗ Brak konfiguracji WordPress. Skonfiguruj połączenie najpierw.{Style.RESET_ALL}")
            return
        
        # Uruchom importer
        importer = WordPressImporter()
        importer.run_import()
    
    def show_recent_posts(self):
        """Wyświetla ostatnie posty"""
        print(f"\n{Fore.CYAN}📄 OSTATNIE POSTY WORDPRESS{Style.RESET_ALL}")
        
        config = self.load_config()
        if not config or not all(config.get(k) for k in ['url', 'username', 'password']):
            print(f"{Fore.RED}✗ Brak konfiguracji WordPress{Style.RESET_ALL}")
            return
        
        try:
            client = WordPressClient(
                config['url'], 
                config['username'], 
                config['password']
            )
            
            posts = client.get_posts(10)
            
            if posts:
                print(f"\n{Fore.GREEN}Ostatnie 10 postów:{Style.RESET_ALL}")
                for i, post in enumerate(posts, 1):
                    title = post.get('title', {}).get('rendered', 'Bez tytułu')
                    status = post.get('status', 'unknown')
                    date = post.get('date', '')
                    
                    # Formatuj datę
                    if date:
                        try:
                            dt = datetime.fromisoformat(date.replace('Z', '+00:00'))
                            date_str = dt.strftime('%Y-%m-%d %H:%M')
                        except:
                            date_str = date
                    else:
                        date_str = 'Brak daty'
                    
                    # Koloruj status
                    if status == 'publish':
                        status_color = Fore.GREEN
                    elif status == 'draft':
                        status_color = Fore.YELLOW
                    elif status == 'future':
                        status_color = Fore.BLUE
                    else:
                        status_color = Fore.WHITE
                    
                    print(f"{i:2d}. {title[:50]}...")
                    print(f"     Status: {status_color}{status}{Style.RESET_ALL} | Data: {date_str}")
            else:
                print(f"{Fore.YELLOW}⚠ Nie znaleziono postów{Style.RESET_ALL}")
        
        except Exception as e:
            print(f"{Fore.RED}✗ Błąd pobierania postów: {e}{Style.RESET_ALL}")
    
    def show_menu(self):
        """Wyświetla menu główne"""
        print(f"\n{Fore.BLUE}📋 MENU GŁÓWNE:{Style.RESET_ALL}")
        print("1. 🔧 Konfiguruj połączenie WordPress")
        print("2. 🚀 Szybki import artykułów")
        print("3. 📝 Zaawansowany import (pełne opcje)")
        print("4. 📄 Pokaż ostatnie posty")
        print("5. 🔗 Status połączenia")
        print("6. ❌ Wyjście")
        print()
    
    def run(self):
        """Główna pętla menedżera"""
        while True:
            clear_screen()
            self.print_header()
            self.show_connection_status()
            self.show_menu()
            
            choice = get_user_input("Wybierz opcję (1-6): ")
            
            try:
                if choice == "1":
                    self.configure_wordpress()
                elif choice == "2":
                    self.quick_import()
                elif choice == "3":
                    importer = WordPressImporter()
                    importer.run_import()
                elif choice == "4":
                    self.show_recent_posts()
                elif choice == "5":
                    print(f"\n{Fore.CYAN}🔄 Odświeżanie statusu...{Style.RESET_ALL}")
                    continue  # Odświeży ekran
                elif choice == "6":
                    print(f"\n{Fore.GREEN}👋 Do widzenia!{Style.RESET_ALL}")
                    break
                else:
                    print(f"{Fore.RED}❌ Nieprawidłowy wybór! Wybierz 1-6.{Style.RESET_ALL}")
                
                if choice != "6":
                    input(f"\n{Fore.CYAN}Naciśnij Enter aby kontynuować...{Style.RESET_ALL}")
            
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 Przerwano przez użytkownika{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"\n{Fore.RED}❌ Błąd: {e}{Style.RESET_ALL}")
                input(f"\n{Fore.CYAN}Naciśnij Enter aby kontynuować...{Style.RESET_ALL}")

def main():
    """Główna funkcja"""
    manager = WordPressManager()
    manager.run()

if __name__ == "__main__":
    main()
