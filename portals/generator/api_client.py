#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from typing import Dict, List, Optional, Any
from colorama import Fore, Style

class OpenRouterClient:
    """Klient do komunikacji z OpenRouter API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
    
    def test_connection(self) -> bool:
        """Testuje połączenie z API"""
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"{Fore.GREEN}✓ Połączenie z OpenRouter działa poprawnie{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}✗ Błąd połączenia: {response.status_code}{Style.RESET_ALL}")
                return False
                
        except requests.RequestException as e:
            print(f"{Fore.RED}✗ Błąd połączenia: {e}{Style.RESET_ALL}")
            return False
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Pobiera listę dostępnych modeli"""
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("data", [])
            else:
                print(f"{Fore.RED}Błąd pobierania modeli: {response.status_code}{Style.RESET_ALL}")
                return []
                
        except requests.RequestException as e:
            print(f"{Fore.RED}Błąd pobierania modeli: {e}{Style.RESET_ALL}")
            return []
    
    def generate_content(self, prompt: str, model: str, max_tokens: int = 4000) -> Optional[str]:
        """Generuje treść używając wybranego modelu"""
        try:
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "system",
                        "content": "Jesteś ekspertem w tworzeniu wysokiej jakości artykułów SEO dla firm sprzątających. Tworzysz treści profesjonalne, wartościowe i zoptymalizowane pod kątem wyszukiwarek."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": max_tokens
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                message = data["choices"][0]["message"]

                # Obsługa modeli reasoning (np. Qwen3-8b)
                content = message.get("content", "")
                reasoning = message.get("reasoning", "")

                # Jeśli content jest pusty, ale reasoning zawiera treść, użyj reasoning
                if not content.strip() and reasoning.strip():
                    return reasoning
                elif content.strip():
                    return content
                else:
                    print(f"{Fore.YELLOW}⚠ Otrzymano pustą odpowiedź z API{Style.RESET_ALL}")
                    return None
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                print(f"{Fore.RED}Błąd API: {error_msg}{Style.RESET_ALL}")
                return None
                
        except requests.RequestException as e:
            print(f"{Fore.RED}Błąd połączenia z API: {e}{Style.RESET_ALL}")
            return None
        except (KeyError, json.JSONDecodeError) as e:
            print(f"{Fore.RED}Błąd parsowania odpowiedzi API: {e}{Style.RESET_ALL}")
            return None
    
    def generate_titles(self, topic: str, count: int = 5) -> List[str]:
        """Generuje tytuły artykułów na podstawie tematu"""
        prompt = f"""Wygeneruj {count} unikalnych, atrakcyjnych tytułów artykułów na blog firmy sprzątającej na temat: {topic}.

Tytuły powinny być:
- Zróżnicowane tematycznie
- Przyciągające uwagę
- Zawierające słowa kluczowe związane ze sprzątaniem
- Każdy tytuł w nowej linii
- Bez numeracji

Zwróć tylko listę tytułów, bez dodatkowego tekstu."""

        content = self.generate_content(prompt, "openai/gpt-3.5-turbo", max_tokens=500)
        
        if content:
            titles = [
                line.strip().replace('"', '').replace("'", '')
                for line in content.split('\n')
                if line.strip() and not line.strip().startswith(('-', '*', '1.', '2.', '3.', '4.', '5.'))
            ]
            return titles[:count]
        
        return []
