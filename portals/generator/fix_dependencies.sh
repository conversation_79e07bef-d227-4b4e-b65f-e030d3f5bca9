#!/bin/bash

# Skrypt naprawy zależności dla systemów z PEP 668
# Autor: AI Assistant

# Ko<PERSON>y
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🔧 NAPRAWA ZALEŻNOŚCI PYTHON${NC}"
echo -e "${BLUE}Rozwiązywanie problemu z PEP 668...${NC}"
echo

# Sprawdź system operacyjny
if [ -f /etc/debian_version ]; then
    OS="debian"
    echo -e "${GREEN}✅ Wykryto system: Debian/Ubuntu${NC}"
elif [ -f /etc/redhat-release ]; then
    OS="redhat"
    echo -e "${GREEN}✅ Wykryto system: RedHat/CentOS${NC}"
else
    OS="unknown"
    echo -e "${YELLOW}⚠️  Nieznany system operacyjny${NC}"
fi

echo

# Metoda 1: Pakiety systemowe (zalecane)
echo -e "${CYAN}📦 Metoda 1: Instalacja pakietów systemowych${NC}"

case $OS in
    "debian")
        echo "Aktualizuję listę pakietów..."
        apt update
        
        echo "Instaluję pakiety Python..."
        apt install -y python3-requests python3-colorama python3-bs4 python3-dotenv
        ;;
    "redhat")
        echo "Instaluję pakiety Python..."
        yum install -y python3-requests python3-colorama python3-beautifulsoup4
        ;;
    *)
        echo -e "${YELLOW}⚠️  Nieznany system - przechodzę do metody 2${NC}"
        ;;
esac

echo

# Sprawdź czy pakiety systemowe działają
echo -e "${CYAN}🧪 Testowanie pakietów systemowych...${NC}"

python3 -c "
try:
    import requests
    print('✅ requests - OK')
except ImportError:
    print('❌ requests - BRAK')

try:
    import colorama
    print('✅ colorama - OK')
except ImportError:
    print('❌ colorama - BRAK')

try:
    import json, os, sys, time, datetime, signal, re
    print('✅ moduły standardowe - OK')
except ImportError:
    print('❌ moduły standardowe - PROBLEM')
"

echo

# Metoda 2: pip z --break-system-packages (jeśli pakiety systemowe nie działają)
echo -e "${CYAN}📦 Metoda 2: pip z --break-system-packages${NC}"

if pip3 install -r requirements.txt --break-system-packages --quiet 2>/dev/null; then
    echo -e "${GREEN}✅ Pakiety zainstalowane przez pip${NC}"
else
    echo -e "${YELLOW}⚠️  pip z --break-system-packages nie działa${NC}"
fi

echo

# Metoda 3: pip --user (instalacja dla użytkownika)
echo -e "${CYAN}📦 Metoda 3: pip --user${NC}"

if pip3 install -r requirements.txt --user --quiet 2>/dev/null; then
    echo -e "${GREEN}✅ Pakiety zainstalowane dla użytkownika${NC}"
else
    echo -e "${YELLOW}⚠️  pip --user nie działa${NC}"
fi

echo

# Test końcowy
echo -e "${CYAN}🧪 TEST KOŃCOWY${NC}"
echo -e "${BLUE}Sprawdzam czy generator może działać...${NC}"

python3 test_setup.py

echo
echo -e "${GREEN}🎉 Naprawa zależności zakończona!${NC}"
echo -e "${YELLOW}Jeśli nadal są problemy, uruchom generator i sprawdź błędy.${NC}"
echo
echo -e "${CYAN}Następne kroki:${NC}"
echo "1. ./run_generator.sh"
echo "2. python3 generator.py"
