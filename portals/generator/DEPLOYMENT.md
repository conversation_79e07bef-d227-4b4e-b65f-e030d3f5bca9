# 🚀 Instrukcja wdrożenia Generatora Artykułów AI na VPS

## 📋 Przygotowanie VPS

### 1. Wymagania systemowe
- **System:** Ubuntu 20.04+ / Debian 10+ / CentOS 7+
- **RAM:** Minimum 1GB (zalecane 2GB+)
- **Dysk:** Minimum 5GB wolnego miejsca
- **Python:** 3.6 lub nowszy
- **Połączenie:** Stabilny internet

### 2. Aktualizacja systemu
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 3. Instalacja wymaganych pakietów
```bash
# Ubuntu/Debian
sudo apt install -y python3 python3-pip screen git curl

# CentOS/RHEL
sudo yum install -y python3 python3-pip screen git curl
```

## 📦 Instalacja generatora

### Metoda 1: Automatyczna instalacja
```bash
cd /www/portals/generator
./install.sh
```

### Metoda 2: Ręczna instalacja
```bash
# 1. Przejdź do katalogu
cd /www/portals/generator

# 2. Zainstaluj zależności Python
pip3 install -r requirements.txt

# 3. Ustaw uprawnienia
chmod +x *.sh *.py

# 4. Utwórz folder wyjściowy
mkdir -p artykuly

# 5. Przetestuj instalację
python3 test_setup.py
```

## 🔑 Konfiguracja klucza API

### 1. Uzyskaj klucz OpenRouter
- Zarejestruj się na https://openrouter.ai
- Przejdź do sekcji API Keys
- Wygeneruj nowy klucz (zaczyna się od `sk-or-`)

### 2. Skonfiguruj generator
```bash
# Skopiuj przykładową konfigurację
cp generator_config.json.example generator_config.json

# Edytuj konfigurację
nano generator_config.json
```

### 3. Wstaw klucz API
```json
{
  "openrouter_api_key": "sk-or-v1-TWÓJ_KLUCZ_TUTAJ",
  "model": "openai/gpt-3.5-turbo",
  "output_folder": "artykuly",
  "article_length": "long",
  "tone": "professional"
}
```

## 🚀 Uruchamianie generatora

### Metoda 1: Menedżer sesji (zalecane)
```bash
./run_generator.sh
```

### Metoda 2: Quick Start (tematy predefiniowane)
```bash
./quick_start.sh
```

### Metoda 3: Bezpośrednie uruchomienie
```bash
python3 generator.py
```

### Metoda 4: Ręczne screen
```bash
# Uruchom nową sesję
screen -S generator_nazwa

# W sesji uruchom generator
python3 generator.py

# Odłącz się: Ctrl+A, potem D
# Dołącz ponownie: screen -r generator_nazwa
```

## 📊 Zarządzanie wieloma instancjami

### Przykład 50 instancji tematycznych:
```bash
# Uruchom quick start
./quick_start.sh

# Wybierz "all" aby uruchomić wszystkie tematy
# Lub wybierz konkretne numery: 1,2,3,4,5
```

### Ręczne tworzenie instancji:
```bash
# Instancja 1: Sprzątanie biur
screen -S generator_biura
python3 generator.py

# Instancja 2: Sprzątanie domów  
screen -S generator_domy
python3 generator.py

# Instancja 3: Czyszczenie dywanów
screen -S generator_dywany
python3 generator.py

# ... i tak dalej
```

### Zarządzanie sesjami:
```bash
# Lista wszystkich sesji
screen -ls

# Dołącz do sesji
screen -r generator_biura

# Zatrzymaj sesję (z poziomu sesji)
exit

# Zatrzymaj sesję (z zewnątrz)
screen -S generator_biura -X quit

# Zatrzymaj wszystkie sesje generatora
screen -ls | grep generator_ | cut -d. -f1 | awk '{print $1}' | xargs -I {} screen -S {} -X quit
```

## 📁 Organizacja folderów

### Struktura katalogów:
```
/www/portals/generator/
├── generator.py              # Główny skrypt
├── config.py                 # Zarządzanie konfiguracją
├── api_client.py            # Klient OpenRouter API
├── article_generator.py     # Logika generowania
├── utils.py                 # Funkcje pomocnicze
├── run_generator.sh         # Menedżer sesji
├── quick_start.sh           # Szybki start
├── install.sh               # Instalator
├── test_setup.py           # Testy konfiguracji
├── requirements.txt         # Zależności Python
├── generator_config.json    # Konfiguracja główna
├── generator.log           # Logi aplikacji
└── artykuly/               # Folder wyjściowy (domyślny)
    ├── ciekawostki/        # Podfolder tematyczny
    ├── porady/             # Podfolder tematyczny
    └── ...
```

### Konfiguracja folderów tematycznych:
```bash
# Każda instancja może mieć własny folder
# Konfiguracja w generator_config.json:
{
  "output_folder": "artykuly/ciekawostki"
}
```

## 🔍 Monitorowanie i logi

### Podgląd logów w czasie rzeczywistym:
```bash
# Główny log
tail -f generator.log

# Logi systemowe
journalctl -f -u screen

# Logi konkretnej sesji
screen -S generator_biura -X hardcopy /tmp/session.log
tail -f /tmp/session.log
```

### Monitoring zasobów:
```bash
# Użycie CPU i RAM
htop

# Procesy screen
ps aux | grep screen

# Użycie dysku
df -h
du -sh artykuly/
```

### Automatyczne logi:
```bash
# Dodaj do crontab dla regularnych raportów
crontab -e

# Dodaj linię (raport co godzinę):
0 * * * * /usr/bin/screen -ls | grep generator_ > /tmp/generator_status.log
```

## ⚙️ Automatyzacja i skrypty

### Skrypt autostartu (systemd):
```bash
# Utwórz plik serwisu
sudo nano /etc/systemd/system/generator-manager.service
```

```ini
[Unit]
Description=Generator Artykułów AI Manager
After=network.target

[Service]
Type=forking
User=www-data
WorkingDirectory=/www/portals/generator
ExecStart=/www/portals/generator/run_generator.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Włącz serwis
sudo systemctl enable generator-manager
sudo systemctl start generator-manager
```

### Skrypt backup:
```bash
#!/bin/bash
# backup_articles.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/generator"
SOURCE_DIR="/www/portals/generator/artykuly"

mkdir -p $BACKUP_DIR
tar -czf "$BACKUP_DIR/articles_$DATE.tar.gz" -C "$SOURCE_DIR" .

# Usuń backupy starsze niż 7 dni
find $BACKUP_DIR -name "articles_*.tar.gz" -mtime +7 -delete
```

### Skrypt czyszczenia:
```bash
#!/bin/bash
# cleanup.sh

# Usuń stare logi (starsze niż 30 dni)
find /www/portals/generator -name "*.log" -mtime +30 -delete

# Usuń pliki tymczasowe
rm -f /tmp/start_*.sh
rm -f /tmp/session.log

# Wyczyść cache Python
find /www/portals/generator -name "__pycache__" -type d -exec rm -rf {} +
```

## 🚨 Rozwiązywanie problemów

### Problemy z API:
```bash
# Test połączenia
curl -H "Authorization: Bearer sk-or-v1-TWÓJ_KLUCZ" https://openrouter.ai/api/v1/models

# Test generatora
python3 -c "
from api_client import OpenRouterClient
client = OpenRouterClient('sk-or-v1-TWÓJ_KLUCZ')
print(client.test_connection())
"
```

### Problemy z screen:
```bash
# Sprawdź czy screen działa
screen -v

# Wyczyść martwe sesje
screen -wipe

# Sprawdź limity użytkownika
ulimit -a
```

### Problemy z pamięcią:
```bash
# Sprawdź użycie pamięci
free -h

# Sprawdź procesy Python
ps aux | grep python3

# Ograniczenie pamięci dla procesu
ulimit -v 1048576  # 1GB w KB
```

### Problemy z dyskiem:
```bash
# Sprawdź miejsce
df -h

# Znajdź duże pliki
find /www/portals/generator -size +100M -ls

# Wyczyść logi
> generator.log
```

## 📈 Optymalizacja wydajności

### Ustawienia dla wielu instancji:
```json
{
  "delay_minutes": 1,
  "delay_seconds": 30,
  "model": "openai/gpt-3.5-turbo"
}
```

### Limity systemowe:
```bash
# Zwiększ limit plików dla użytkownika
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# Zwiększ limit procesów
echo "* soft nproc 32768" >> /etc/security/limits.conf
echo "* hard nproc 32768" >> /etc/security/limits.conf
```

### Monitoring zasobów:
```bash
# Zainstaluj monitoring
sudo apt install htop iotop nethogs

# Monitoruj w czasie rzeczywistym
htop
iotop
nethogs
```

## 🔒 Bezpieczeństwo

### Zabezpieczenie kluczy API:
```bash
# Ustaw odpowiednie uprawnienia
chmod 600 generator_config.json

# Utwórz backup zaszyfrowany
gpg -c generator_config.json
```

### Firewall:
```bash
# Podstawowa konfiguracja UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### Aktualizacje:
```bash
# Automatyczne aktualizacje bezpieczeństwa
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

---

## 📞 Wsparcie techniczne

W przypadku problemów:
1. Sprawdź logi: `tail -f generator.log`
2. Przetestuj konfigurację: `python3 test_setup.py`
3. Sprawdź połączenie API
4. Zweryfikuj uprawnienia plików
5. Sprawdź zasoby systemowe

**Powodzenia w wdrożeniu!** 🚀
