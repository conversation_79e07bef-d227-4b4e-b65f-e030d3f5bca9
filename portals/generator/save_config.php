<?php
// Sprawdź, czy ż<PERSON>danie jest typu POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Metoda nie dozwolona']);
    exit;
}

// Pobierz dane JSON z żądania
$json_data = file_get_contents('php://input');
$config = json_decode($json_data, true);

// Sprawdź, czy dane są poprawne
if (!$config) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Nieprawidłowe dane JSON']);
    exit;
}

// Zapisz konfigurację do pliku
$result = file_put_contents('config.json', $json_data);

if ($result === false) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['success' => false, 'message' => '<PERSON>e można zapisać pliku konfiguracyjnego']);
    exit;
}

// Zwr<PERSON>ć sukces
header('Content-Type: application/json');
echo json_encode(['success' => true, 'message' => 'Konfiguracja zapisana pomyślnie']);
?>
