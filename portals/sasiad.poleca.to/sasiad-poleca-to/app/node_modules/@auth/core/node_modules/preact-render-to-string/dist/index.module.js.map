{"version": 3, "file": "index.module.js", "sources": ["../src/util.js", "../src/pretty.js", "../src/index.js"], "sourcesContent": ["// DOM properties that should NOT have \"px\" added when numeric\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;\nexport const VOID_ELEMENTS = /^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const XLINK = /^xlink:?./;\n\nconst ENCODED_ENTITIES = /[\"&<]/;\n\nexport function encodeEntities(str) {\n\t// Ensure we're always parsing and returning a string:\n\tstr += '';\n\n\t// Skip all work for strings with no entities needing encoding:\n\tif (ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst CSS_REGEX = /([A-Z])/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tif (str) str += ' ';\n\t\t\t// str += jsToCss(prop);\n\t\t\tstr +=\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$1').toLowerCase());\n\n\t\t\tif (typeof val === 'number' && IS_NON_DIMENSIONAL.test(prop) === false) {\n\t\t\t\tstr = str + ': ' + val + 'px;';\n\t\t\t} else {\n\t\t\t\tstr = str + ': ' + val + ';';\n\t\t\t}\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: []\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n", "import {\n\tencodeEntities,\n\tindent,\n\tisLargeString,\n\tstyleObjToCss,\n\tgetChildren,\n\tcreateComponent,\n\tgetContext,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\n\n// components without names, kept as a hash for later comparison to return consistent UnnamedComponentXX names.\nconst UNNAMED = [];\n\nexport function _renderToStringPretty(\n\tvnode,\n\tcontext,\n\topts,\n\tinner,\n\tisSvgMode,\n\tselectValue\n) {\n\tif (vnode == null || typeof vnode === 'boolean') {\n\t\treturn '';\n\t}\n\n\t// #text nodes\n\tif (typeof vnode !== 'object') {\n\t\treturn encodeEntities(vnode);\n\t}\n\n\tlet pretty = opts.pretty,\n\t\tindentChar = pretty && typeof pretty === 'string' ? pretty : '\\t';\n\n\tif (Array.isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tif (pretty && i > 0) rendered = rendered + '\\n';\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToStringPretty(\n\t\t\t\t\tvnode[i],\n\t\t\t\t\tcontext,\n\t\t\t\t\topts,\n\t\t\t\t\tinner,\n\t\t\t\t\tisSvgMode,\n\t\t\t\t\tselectValue\n\t\t\t\t);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\tlet nodeName = vnode.type,\n\t\tprops = vnode.props,\n\t\tisComponent = false;\n\n\t// components\n\tif (typeof nodeName === 'function') {\n\t\tisComponent = true;\n\t\tif (opts.shallow && (inner || opts.renderRootComponent === false)) {\n\t\t\tnodeName = getComponentName(nodeName);\n\t\t} else if (nodeName === Fragment) {\n\t\t\tconst children = [];\n\t\t\tgetChildren(children, vnode.props.children);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\tchildren,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t} else {\n\t\t\tlet rendered;\n\n\t\t\tlet c = (vnode.__c = createComponent(vnode, context));\n\n\t\t\t// options._diff\n\t\t\tif (options.__b) options.__b(vnode);\n\n\t\t\t// options._render\n\t\t\tlet renderHook = options.__r;\n\n\t\t\tif (\n\t\t\t\t!nodeName.prototype ||\n\t\t\t\ttypeof nodeName.prototype.render !== 'function'\n\t\t\t) {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (c.__d && count++ < 25) {\n\t\t\t\t\tc.__d = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\t// stateless functional components\n\t\t\t\t\trendered = nodeName.call(vnode.__c, props, cctx);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// c = new nodeName(props, context);\n\t\t\t\tc = vnode.__c = new nodeName(props, cctx);\n\t\t\t\tc.__v = vnode;\n\t\t\t\t// turn off stateful re-rendering:\n\t\t\t\tc._dirty = c.__d = true;\n\t\t\t\tc.props = props;\n\t\t\t\tif (c.state == null) c.state = {};\n\n\t\t\t\tif (c._nextState == null && c.__s == null) {\n\t\t\t\t\tc._nextState = c.__s = c.state;\n\t\t\t\t}\n\n\t\t\t\tc.context = cctx;\n\t\t\t\tif (nodeName.getDerivedStateFromProps)\n\t\t\t\t\tc.state = Object.assign(\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tc.state,\n\t\t\t\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t\t\t\t);\n\t\t\t\telse if (c.componentWillMount) {\n\t\t\t\t\tc.componentWillMount();\n\n\t\t\t\t\t// If the user called setState in cWM we need to flush pending,\n\t\t\t\t\t// state updates. This is the same behaviour in React.\n\t\t\t\t\tc.state =\n\t\t\t\t\t\tc._nextState !== c.state\n\t\t\t\t\t\t\t? c._nextState\n\t\t\t\t\t\t\t: c.__s !== c.state\n\t\t\t\t\t\t\t? c.__s\n\t\t\t\t\t\t\t: c.state;\n\t\t\t\t}\n\n\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\trendered = c.render(c.props, c.state, c.context);\n\t\t\t}\n\n\t\t\tif (c.getChildContext) {\n\t\t\t\tcontext = Object.assign({}, context, c.getChildContext());\n\t\t\t}\n\n\t\t\tif (options.diffed) options.diffed(vnode);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\t}\n\n\t// render JSX to HTML\n\tlet s = '<' + nodeName,\n\t\tpropChildren,\n\t\thtml;\n\n\tif (props) {\n\t\tlet attrs = Object.keys(props);\n\n\t\t// allow sorting lexicographically for more determinism (useful for tests, such as via preact-jsx-chai)\n\t\tif (opts && opts.sortAttributes === true) attrs.sort();\n\n\t\tfor (let i = 0; i < attrs.length; i++) {\n\t\t\tlet name = attrs[i],\n\t\t\t\tv = props[name];\n\t\t\tif (name === 'children') {\n\t\t\t\tpropChildren = v;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tif (\n\t\t\t\t!(opts && opts.allAttributes) &&\n\t\t\t\t(name === 'key' ||\n\t\t\t\t\tname === 'ref' ||\n\t\t\t\t\tname === '__self' ||\n\t\t\t\t\tname === '__source')\n\t\t\t)\n\t\t\t\tcontinue;\n\n\t\t\tif (name === 'defaultValue') {\n\t\t\t\tname = 'value';\n\t\t\t} else if (name === 'defaultChecked') {\n\t\t\t\tname = 'checked';\n\t\t\t} else if (name === 'defaultSelected') {\n\t\t\t\tname = 'selected';\n\t\t\t} else if (name === 'className') {\n\t\t\t\tif (typeof props.class !== 'undefined') continue;\n\t\t\t\tname = 'class';\n\t\t\t} else if (isSvgMode && XLINK.test(name)) {\n\t\t\t\tname = name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t\t\t}\n\n\t\t\tif (name === 'htmlFor') {\n\t\t\t\tif (props.for) continue;\n\t\t\t\tname = 'for';\n\t\t\t}\n\n\t\t\tif (name === 'style' && v && typeof v === 'object') {\n\t\t\t\tv = styleObjToCss(v);\n\t\t\t}\n\n\t\t\t// always use string values instead of booleans for aria attributes\n\t\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\t\tif (name[0] === 'a' && name['1'] === 'r' && typeof v === 'boolean') {\n\t\t\t\tv = String(v);\n\t\t\t}\n\n\t\t\tlet hooked =\n\t\t\t\topts.attributeHook &&\n\t\t\t\topts.attributeHook(name, v, context, opts, isComponent);\n\t\t\tif (hooked || hooked === '') {\n\t\t\t\ts = s + hooked;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (nodeName === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tpropChildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\t// in non-xml mode, allow boolean attributes\n\t\t\t\t\tif (!opts || !opts.xml) {\n\t\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (nodeName === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\tnodeName === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\ttypeof props.selected === 'undefined'\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ` selected`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ` ${name}=\"${encodeEntities(v)}\"`;\n\t\t\t}\n\t\t}\n\t}\n\n\t// account for >1 multiline attribute\n\tif (pretty) {\n\t\tlet sub = s.replace(/\\n\\s*/, ' ');\n\t\tif (sub !== s && !~sub.indexOf('\\n')) s = sub;\n\t\telse if (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t}\n\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(nodeName))\n\t\tthrow new Error(`${nodeName} is not a valid HTML tag name in ${s}`);\n\n\tlet isVoid =\n\t\tVOID_ELEMENTS.test(nodeName) ||\n\t\t(opts.voidElements && opts.voidElements.test(nodeName));\n\tlet pieces = [];\n\n\tlet children;\n\tif (html) {\n\t\t// if multiline, indent.\n\t\tif (pretty && isLargeString(html)) {\n\t\t\thtml = '\\n' + indentChar + indent(html, indentChar);\n\t\t}\n\t\ts = s + html;\n\t} else if (\n\t\tpropChildren != null &&\n\t\tgetChildren((children = []), propChildren).length\n\t) {\n\t\tlet hasLarge = pretty && ~s.indexOf('\\n');\n\t\tlet lastWasText = false;\n\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\t\tnodeName === 'svg'\n\t\t\t\t\t\t\t? true\n\t\t\t\t\t\t\t: nodeName === 'foreignObject'\n\t\t\t\t\t\t\t? false\n\t\t\t\t\t\t\t: isSvgMode,\n\t\t\t\t\tret = _renderToStringPretty(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\topts,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tchildSvgMode,\n\t\t\t\t\t\tselectValue\n\t\t\t\t\t);\n\n\t\t\t\tif (pretty && !hasLarge && isLargeString(ret)) hasLarge = true;\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tif (pretty) {\n\t\t\t\t\t\tlet isText = ret.length > 0 && ret[0] != '<';\n\n\t\t\t\t\t\t// We merge adjacent text nodes, otherwise each piece would be printed\n\t\t\t\t\t\t// on a new line.\n\t\t\t\t\t\tif (lastWasText && isText) {\n\t\t\t\t\t\t\tpieces[pieces.length - 1] += ret;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlastWasText = isText;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (pretty && hasLarge) {\n\t\t\tfor (let i = pieces.length; i--; ) {\n\t\t\t\tpieces[i] = '\\n' + indentChar + indent(pieces[i], indentChar);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (pieces.length || html) {\n\t\ts = s + pieces.join('');\n\t} else if (opts && opts.xml) {\n\t\treturn s.substring(0, s.length - 1) + ' />';\n\t}\n\n\tif (isVoid && !children && !html) {\n\t\ts = s.replace(/>$/, ' />');\n\t} else {\n\t\tif (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t\ts = s + `</${nodeName}>`;\n\t}\n\n\treturn s;\n}\n\nfunction getComponentName(component) {\n\treturn (\n\t\tcomponent.displayName ||\n\t\t(component !== Function && component.name) ||\n\t\tgetFallbackComponentName(component)\n\t);\n}\n\nfunction getFallbackComponentName(component) {\n\tlet str = Function.prototype.toString.call(component),\n\t\tname = (str.match(/^\\s*function\\s+([^( ]+)/) || '')[1];\n\tif (!name) {\n\t\t// search for an existing indexed name for the given component:\n\t\tlet index = -1;\n\t\tfor (let i = UNNAMED.length; i--; ) {\n\t\t\tif (UNNAMED[i] === component) {\n\t\t\t\tindex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// not found, create a new indexed name:\n\t\tif (index < 0) {\n\t\t\tindex = UNNAMED.push(component) - 1;\n\t\t}\n\t\tname = `UnnamedComponent${index}`;\n\t}\n\treturn name;\n}\n", "import {\n\tencodeEntities,\n\tstyleObjTo<PERSON><PERSON>,\n\tget<PERSON>ontext,\n\tcreate<PERSON>omponent,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\nimport { _renderToStringPretty } from './pretty';\nimport {\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE\n} from './constants';\n\n/** @typedef {import('preact').VNode} VNode */\n\nconst SHALLOW = { shallow: true };\n\n/** Render Preact JSX + Components to an HTML string.\n *\t@name render\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n *\t@param {Object} [options={}]\tRendering options\n *\t@param {Boolean} [options.shallow=false]\tIf `true`, renders nested Components as HTML elements (`<Foo a=\"b\" />`).\n *\t@param {Boolean} [options.xml=false]\t\tIf `true`, uses self-closing tags for elements without children.\n *\t@param {Boolean} [options.pretty=false]\t\tIf `true`, adds whitespace for readability\n *\t@param {RegExp|undefined} [options.voidElements]       RegeEx that matches elements that are considered void (self-closing)\n */\nrenderToString.render = renderToString;\n\n/** Only render elements, leaving Components inline as `<ComponentName ... />`.\n *\tThis method is just a convenience alias for `render(vnode, context, { shallow:true })`\n *\t@name shallow\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n */\nlet shallowRender = (vnode, context) => renderToString(vnode, context, SHALLOW);\n\nconst EMPTY_ARR = [];\nfunction renderToString(vnode, context, opts) {\n\tcontext = context || {};\n\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\tlet res;\n\tif (\n\t\topts &&\n\t\t(opts.pretty ||\n\t\t\topts.voidElements ||\n\t\t\topts.sortAttributes ||\n\t\t\topts.shallow ||\n\t\t\topts.allAttributes ||\n\t\t\topts.xml ||\n\t\t\topts.attributeHook)\n\t) {\n\t\tres = _renderToStringPretty(vnode, context, opts);\n\t} else {\n\t\tres = _renderToString(vnode, context, false, undefined);\n\t}\n\n\t// options._commit, we don't schedule any effects in this library right now,\n\t// so we can pass an empty queue to this hook.\n\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\tEMPTY_ARR.length = 0;\n\treturn res;\n}\n\nfunction renderFunctionComponent(vnode, context) {\n\tlet rendered,\n\t\tc = createComponent(vnode, context),\n\t\tcctx = getContext(vnode.type, context);\n\n\tvnode[COMPONENT] = c;\n\n\t// If a hook invokes setState() to invalidate the component during rendering,\n\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t// Note:\n\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\tlet renderHook = options[RENDER];\n\tlet count = 0;\n\twhile (c[DIRTY] && count++ < 25) {\n\t\tc[DIRTY] = false;\n\n\t\tif (renderHook) renderHook(vnode);\n\n\t\t// stateless functional components\n\t\trendered = vnode.type.call(c, vnode.props, cctx);\n\t}\n\n\treturn rendered;\n}\n\nfunction renderClassComponent(vnode, context) {\n\tlet nodeName = vnode.type,\n\t\tcctx = getContext(nodeName, context);\n\n\t// c = new nodeName(props, context);\n\tlet c = new nodeName(vnode.props, cctx);\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\tc.props = vnode.props;\n\tif (c.state == null) c.state = {};\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tc.context = cctx;\n\tif (nodeName.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t}\n\n\tlet renderHook = options[RENDER];\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, c.context);\n}\n\nfunction normalizePropName(name, isSvgMode) {\n\tif (name === 'className') {\n\t\treturn 'class';\n\t} else if (name === 'htmlFor') {\n\t\treturn 'for';\n\t} else if (name === 'defaultValue') {\n\t\treturn 'value';\n\t} else if (name === 'defaultChecked') {\n\t\treturn 'checked';\n\t} else if (name === 'defaultSelected') {\n\t\treturn 'selected';\n\t} else if (isSvgMode && XLINK.test(name)) {\n\t\treturn name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t}\n\n\treturn name;\n}\n\nfunction normalizePropValue(name, v) {\n\tif (name === 'style' && v != null && typeof v === 'object') {\n\t\treturn styleObjToCss(v);\n\t} else if (name[0] === 'a' && name[1] === 'r' && typeof v === 'boolean') {\n\t\t// always use string values instead of booleans for aria attributes\n\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\treturn String(v);\n\t}\n\n\treturn v;\n}\n\nconst isArray = Array.isArray;\nconst assign = Object.assign;\n\n/** The default export is an alias of `render()`. */\nfunction _renderToString(vnode, context, isSvgMode, selectValue) {\n\t// Ignore non-rendered VNodes/values\n\tif (vnode == null || vnode === true || vnode === false || vnode === '') {\n\t\treturn '';\n\t}\n\n\t// Text VNodes: escape as HTML\n\tif (typeof vnode !== 'object') {\n\t\treturn encodeEntities(vnode);\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\trendered =\n\t\t\t\trendered + _renderToString(vnode[i], context, isSvgMode, selectValue);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\tif (options[DIFF]) options[DIFF](vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tconst isComponent = typeof type === 'function';\n\tif (isComponent) {\n\t\tif (type === Fragment) {\n\t\t\treturn _renderToString(\n\t\t\t\tvnode.props.children,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\n\t\tlet rendered;\n\t\tif (type.prototype && typeof type.prototype.render === 'function') {\n\t\t\trendered = renderClassComponent(vnode, context);\n\t\t} else {\n\t\t\trendered = renderFunctionComponent(vnode, context);\n\t\t}\n\n\t\tlet component = vnode[COMPONENT];\n\t\tif (component.getChildContext) {\n\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t}\n\n\t\t// Recurse into children before invoking the after-diff hook\n\t\tconst str = _renderToString(rendered, context, isSvgMode, selectValue);\n\t\tif (options[DIFFED]) options[DIFFED](vnode);\n\t\treturn str;\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<',\n\t\tchildren,\n\t\thtml;\n\n\ts = s + type;\n\n\tif (props) {\n\t\tchildren = props.children;\n\t\tfor (let name in props) {\n\t\t\tlet v = props[name];\n\n\t\t\tif (\n\t\t\t\tname === 'key' ||\n\t\t\t\tname === 'ref' ||\n\t\t\t\tname === '__self' ||\n\t\t\t\tname === '__source' ||\n\t\t\t\tname === 'children' ||\n\t\t\t\t(name === 'className' && 'class' in props) ||\n\t\t\t\t(name === 'htmlFor' && 'for' in props)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tname = normalizePropName(name, isSvgMode);\n\t\t\tv = normalizePropValue(name, v);\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (type === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tchildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (type === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\ttype === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\t!('selected' in props)\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ' ' + name + '=\"' + encodeEntities(v) + '\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tlet startElement = s;\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}`);\n\t}\n\n\tlet pieces = '';\n\tlet hasChildren = false;\n\n\tif (html) {\n\t\tpieces = pieces + html;\n\t\thasChildren = true;\n\t} else if (typeof children === 'string') {\n\t\tpieces = pieces + encodeEntities(children);\n\t\thasChildren = true;\n\t} else if (isArray(children)) {\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\t\t\tlet ret = _renderToString(child, context, childSvgMode, selectValue);\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tpieces = pieces + ret;\n\t\t\t\t\thasChildren = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (children != null && children !== false && children !== true) {\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\tlet ret = _renderToString(children, context, childSvgMode, selectValue);\n\n\t\t// Skip if we received an empty string\n\t\tif (ret) {\n\t\t\tpieces = pieces + ret;\n\t\t\thasChildren = true;\n\t\t}\n\t}\n\n\tif (options[DIFFED]) options[DIFFED](vnode);\n\n\tif (hasChildren) {\n\t\ts = s + pieces;\n\t} else if (VOID_ELEMENTS.test(type)) {\n\t\treturn startElement + ' />';\n\t}\n\n\treturn s + '</' + type + '>';\n}\n\n/** The default export is an alias of `render()`. */\n\nrenderToString.shallowRender = shallowRender;\n\nexport default renderToString;\n\nexport {\n\trenderToString as render,\n\trenderToString as renderToStaticMarkup,\n\trenderToString,\n\tshallowRender\n};\n"], "names": ["IS_NON_DIMENSIONAL", "VOID_ELEMENTS", "UNSAFE_NAME", "XLINK", "ENCODED_ENTITIES", "encodeEntities", "str", "test", "last", "i", "out", "ch", "length", "charCodeAt", "slice", "indent", "s", "char", "String", "replace", "isLargeString", "ignoreLines", "indexOf", "JS_TO_CSS", "CSS_REGEX", "styleObjToCss", "prop", "val", "toLowerCase", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accumulator", "children", "Array", "isArray", "reduce", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "getContext", "nodeName", "cxType", "contextType", "provider", "__c", "value", "__", "UNNAMED", "_renderToStringPretty", "opts", "inner", "isSvgMode", "selectValue", "pretty", "indentChar", "rendered", "component", "type", "isComponent", "shallow", "renderRootComponent", "Fragment", "shallowHighOrder", "c", "options", "__b", "renderHook", "__r", "prototype", "render", "cctx", "_dirty", "state", "_nextState", "__s", "getDerivedStateFromProps", "Object", "assign", "componentWillMount", "count", "call", "getChildContext", "diffed", "displayName", "Function", "name", "toString", "match", "index", "getFallbackComponentName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "attrs", "keys", "sortAttributes", "sort", "v", "allAttributes", "hooked", "attributeHook", "__html", "xml", "selected", "sub", "Error", "isVoid", "voidElements", "pieces", "<PERSON><PERSON><PERSON><PERSON>", "lastWasText", "child", "ret", "isText", "join", "substring", "SHALLOW", "renderToString", "shallowRender", "EMPTY_ARR", "res", "previousSkipEffects", "_renderToString", "normalizePropName", "normalizePropValue", "renderClassComponent", "renderFunctionComponent", "startElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "mDACaA,EAAqB,kEACrBC,EAAgB,2EAChBC,EAAc,mBACdC,EAAQ,YAEfC,EAAmB,iBAETC,EAAeC,GAK9B,IAAmC,IAA/BF,EAAiBG,KAHrBD,GAAO,IAGmC,OAAOA,EAQjD,IANA,IAAIE,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIH,EAAIM,OAAQH,IAAK,CAC3B,OAAQH,EAAIO,WAAWJ,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IACvCC,GAAOC,EAEPH,EAAOC,EAAI,EAGZ,OADIA,IAAMD,IAAME,GAAOJ,EAAIQ,MAAMN,EAAMC,IAChCC,MAGGK,EAAS,SAACC,EAAGC,UACvBC,OAAOF,GAAGG,QAAQ,SAAU,MAAQF,GAAQ,QAElCG,EAAgB,SAACJ,EAAGJ,EAAQS,UACtCH,OAAOF,GAAGJ,QAAUA,GAAU,MAC5BS,IAA4C,IAA7BH,OAAOF,GAAGM,QAAQ,QACP,IAA5BJ,OAAOF,GAAGM,QAAQ,MAEbC,EAAY,GAEZC,EAAY,oBAEFC,EAAcT,GAC7B,IAAIV,EAAM,GACV,IAAK,IAAIoB,KAAQV,EAAG,CACnB,IAAIW,EAAMX,EAAEU,GACD,MAAPC,GAAuB,KAARA,IACdrB,IAAKA,GAAO,KAEhBA,GACY,KAAXoB,EAAK,GACFA,EACAH,EAAUG,KACTH,EAAUG,GAAQA,EAAKP,QAAQK,EAAW,OAAOI,eAGrDtB,EADkB,iBAARqB,IAAsD,IAAlC3B,EAAmBO,KAAKmB,GAChDpB,EAAM,KAAOqB,EAAM,MAEnBrB,EAAM,KAAOqB,EAAM,KAI5B,OAAOrB,QAAOuB,WAUCC,EAAYC,EAAaC,GAMxC,OALIC,MAAMC,QAAQF,GACjBA,EAASG,OAAOL,EAAaC,GACP,MAAZC,IAAiC,IAAbA,GAC9BD,EAAYK,KAAKJ,GAEXD,EAGR,SAASM,IACRC,KAAKC,KAAM,WAGIC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,aAMSC,EAAWC,EAAUP,GACpC,IAAIQ,EAASD,EAASE,YAClBC,EAAWF,GAAUR,EAAQQ,EAAOG,KACxC,OAAiB,MAAVH,EACJE,EACCA,EAASR,MAAMU,MACfJ,EAAOK,GACRb,EC5GJ,IAAMc,EAAU,YAEAC,EACfhB,EACAC,EACAgB,EACAC,EACAC,EACAC,GAEA,GAAa,MAATpB,GAAkC,kBAAVA,EAC3B,MAAO,GAIR,GAAqB,iBAAVA,EACV,OAAOpC,EAAeoC,GAGvB,IAAIqB,EAASJ,EAAKI,OACjBC,EAAaD,GAA4B,iBAAXA,EAAsBA,EAAS,KAE9D,GAAI7B,MAAMC,QAAQO,GAAQ,CAEzB,IADA,IAAIuB,EAAW,GACNvD,EAAI,EAAGA,EAAIgC,EAAM7B,OAAQH,IAC7BqD,GAAUrD,EAAI,IAAGuD,GAAsB,MAC3CA,GAECP,EACChB,EAAMhC,GACNiC,EACAgB,EACAC,EACAC,EACAC,GAGH,OAAOG,EAGR,IA8SyBC,EA9SrBhB,EAAWR,EAAMyB,KACpBtB,EAAQH,EAAMG,MACduB,GAAc,EAGf,GAAwB,mBAAblB,EAAyB,CAEnC,GADAkB,GAAc,GACVT,EAAKU,UAAYT,IAAsC,IAA7BD,EAAKW,wBAExBpB,IAAaqB,EAAU,CACjC,IAAMtC,EAAW,GAEjB,OADAF,EAAYE,EAAUS,EAAMG,MAAMZ,UAC3ByB,EACNzB,EACAU,EACAgB,GAC0B,IAA1BA,EAAKa,iBACLX,EACAC,GAGD,IAAIG,EAEAQ,EAAK/B,EAAMY,IAAMb,EAAgBC,EAAOC,GAGxC+B,EAAQC,KAAKD,EAAQC,IAAIjC,GAG7B,IAAIkC,EAAaF,EAAQG,IAEzB,GACE3B,EAAS4B,WAC2B,mBAA9B5B,EAAS4B,UAAUC,OAkBpB,CACN,IAAIC,EAAO/B,EAAWC,EAAUP,IAGhC8B,EAAI/B,EAAMY,IAAM,IAAIJ,EAASL,EAAOmC,IAClCpC,IAAMF,EAER+B,EAAEQ,OAASR,EAAEjC,KAAM,EACnBiC,EAAE5B,MAAQA,EACK,MAAX4B,EAAES,QAAeT,EAAES,MAAQ,IAEX,MAAhBT,EAAEU,YAA+B,MAATV,EAAEW,MAC7BX,EAAEU,WAAaV,EAAEW,IAAMX,EAAES,OAG1BT,EAAE9B,QAAUqC,EACR9B,EAASmC,yBACZZ,EAAES,MAAQI,OAAOC,OAChB,GACAd,EAAES,MACFhC,EAASmC,yBAAyBZ,EAAE5B,MAAO4B,EAAES,QAEtCT,EAAEe,qBACVf,EAAEe,qBAIFf,EAAES,MACDT,EAAEU,aAAeV,EAAES,MAChBT,EAAEU,WACFV,EAAEW,MAAQX,EAAES,MACZT,EAAEW,IACFX,EAAES,OAGHN,GAAYA,EAAWlC,GAE3BuB,EAAWQ,EAAEM,OAAON,EAAE5B,MAAO4B,EAAES,MAAOT,EAAE9B,cA7CxC,IARA,IAAIqC,EAAO/B,EAAWC,EAAUP,GAO5B8C,EAAQ,EACLhB,EAAEjC,KAAOiD,IAAU,IACzBhB,EAAEjC,KAAM,EAEJoC,GAAYA,EAAWlC,GAG3BuB,EAAWf,EAASwC,KAAKhD,EAAMY,IAAKT,EAAOmC,GA+C7C,OALIP,EAAEkB,kBACLhD,EAAU2C,OAAOC,OAAO,GAAI5C,EAAS8B,EAAEkB,oBAGpCjB,EAAQkB,QAAQlB,EAAQkB,OAAOlD,GAC5BgB,EACNO,EACAtB,EACAgB,GAC0B,IAA1BA,EAAKa,iBACLX,EACAC,GA9FDZ,GAsSuBgB,EAtSKhB,GAwSnB2C,aACT3B,IAAc4B,UAAY5B,EAAU6B,MAKvC,SAAkC7B,GACjC,IACC6B,GADSD,SAAShB,UAAUkB,SAASN,KAAKxB,GAC9B+B,MAAM,4BAA8B,IAAI,GACrD,IAAKF,EAAM,CAGV,IADA,IAAIG,GAAS,EACJxF,EAAI+C,EAAQ5C,OAAQH,KAC5B,GAAI+C,EAAQ/C,KAAOwD,EAAW,CAC7BgC,EAAQxF,EACR,MAIEwF,EAAQ,IACXA,EAAQzC,EAAQpB,KAAK6B,GAAa,GAEnC6B,qBAA0BG,EAE3B,OAAOH,EAtBNI,CAAyBjC,GAtM1B,IACCkC,EACAC,EAFGpF,EAAI,IAAMiC,EAId,GAAIL,EAAO,CACV,IAAIyD,EAAQhB,OAAOiB,KAAK1D,GAGpBc,IAAgC,IAAxBA,EAAK6C,gBAAyBF,EAAMG,OAEhD,IAAK,IAAI/F,EAAI,EAAGA,EAAI4F,EAAMzF,OAAQH,IAAK,CACtC,IAAIqF,EAAOO,EAAM5F,GAChBgG,EAAI7D,EAAMkD,GACX,GAAa,aAATA,GAKJ,IAAI5F,EAAYK,KAAKuF,KAGlBpC,GAAQA,EAAKgD,eACL,QAATZ,GACS,QAATA,GACS,WAATA,GACS,aAATA,GALF,CASA,GAAa,iBAATA,EACHA,EAAO,gBACY,mBAATA,EACVA,EAAO,kBACY,oBAATA,EACVA,EAAO,mBACY,cAATA,EAAsB,CAChC,QAA2B,IAAhBlD,QAA6B,SACxCkD,EAAO,aACGlC,GAAazD,EAAMI,KAAKuF,KAClCA,EAAOA,EAAKlE,cAAcT,QAAQ,WAAY,WAG/C,GAAa,YAAT2E,EAAoB,CACvB,GAAIlD,MAAW,SACfkD,EAAO,MAGK,UAATA,GAAoBW,GAAkB,iBAANA,IACnCA,EAAIhF,EAAcgF,IAKH,MAAZX,EAAK,IAA4B,MAAdA,EAAK,IAA6B,kBAANW,IAClDA,EAAIvF,OAAOuF,IAGZ,IAAIE,EACHjD,EAAKkD,eACLlD,EAAKkD,cAAcd,EAAMW,EAAG/D,EAASgB,EAAMS,GAC5C,GAAIwC,GAAqB,KAAXA,EACb3F,GAAQ2F,OAIT,GAAa,4BAATb,EACHM,EAAOK,GAAKA,EAAEI,eACS,aAAb5D,GAAoC,UAAT6C,EAErCK,EAAeM,WACJA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,MAAU,IAANA,GAAoB,KAANA,IACjBA,EAAIX,EAECpC,GAASA,EAAKoD,MAAK,CACvB9F,EAAIA,EAAI,IAAM8E,EACd,SAIF,GAAa,UAATA,EAAkB,CACrB,GAAiB,WAAb7C,EAAuB,CAC1BY,EAAc4C,EACd,SAGa,WAAbxD,GACAY,GAAe4C,QAEW,IAAnB7D,EAAMmE,WAEb/F,gBAGFA,EAAIA,MAAQ8E,OAASzF,EAAeoG,cAhFpCN,EAAeM,GAsFlB,GAAI3C,EAAQ,CACX,IAAIkD,EAAMhG,EAAEG,QAAQ,QAAS,KACzB6F,IAAQhG,IAAOgG,EAAI1F,QAAQ,MACtBwC,IAAW9C,EAAEM,QAAQ,QAAON,GAAQ,MADPA,EAAIgG,EAM3C,GAFAhG,GAAQ,IAEJd,EAAYK,KAAK0C,GACpB,UAAUgE,MAAShE,sCAA4CjC,GAEhE,IAKIgB,EALAkF,EACHjH,EAAcM,KAAK0C,IAClBS,EAAKyD,cAAgBzD,EAAKyD,aAAa5G,KAAK0C,GAC1CmE,EAAS,GAGb,GAAIhB,EAECtC,GAAU1C,EAAcgF,KAC3BA,EAAO,KAAOrC,EAAahD,EAAOqF,EAAMrC,IAEzC/C,GAAQoF,UAEQ,MAAhBD,GACArE,EAAaE,EAAW,GAAKmE,GAAcvF,OAC1C,CAID,IAHA,IAAIyG,EAAWvD,IAAW9C,EAAEM,QAAQ,MAChCgG,GAAc,EAET7G,EAAI,EAAGA,EAAIuB,EAASpB,OAAQH,IAAK,CACzC,IAAI8G,EAAQvF,EAASvB,GAErB,GAAa,MAAT8G,IAA2B,IAAVA,EAAiB,CACrC,IAMCC,EAAM/D,EACL8D,EACA7E,EACAgB,GACA,EATa,QAAbT,GAEgB,kBAAbA,GAEAW,EAOHC,GAMF,GAHIC,IAAWuD,GAAYjG,EAAcoG,KAAMH,GAAW,GAGtDG,EACH,GAAI1D,EAAQ,CACX,IAAI2D,EAASD,EAAI5G,OAAS,GAAe,KAAV4G,EAAI,GAI/BF,GAAeG,EAClBL,EAAOA,EAAOxG,OAAS,IAAM4G,EAE7BJ,EAAOhF,KAAKoF,GAGbF,EAAcG,OAEdL,EAAOhF,KAAKoF,IAKhB,GAAI1D,GAAUuD,EACb,IAAK,IAAI5G,EAAI2G,EAAOxG,OAAQH,KAC3B2G,EAAO3G,GAAK,KAAOsD,EAAahD,EAAOqG,EAAO3G,GAAIsD,GAKrD,GAAIqD,EAAOxG,QAAUwF,EACpBpF,GAAQoG,EAAOM,KAAK,YACVhE,GAAQA,EAAKoD,IACvB,OAAO9F,EAAE2G,UAAU,EAAG3G,EAAEJ,OAAS,GAAK,MAUvC,OAPIsG,GAAWlF,GAAaoE,GAGvBtC,IAAW9C,EAAEM,QAAQ,QAAON,GAAQ,MACxCA,EAAIA,OAASiC,OAHbjC,EAAIA,EAAEG,QAAQ,KAAM,OAMdH,MCzUF4G,EAAU,CAAExD,SAAS,GAa3ByD,EAAe/C,OAAS+C,EASpBC,IAAAA,EAAgB,SAACrF,EAAOC,UAAYmF,EAAepF,EAAOC,EAASkF,IAEjEG,EAAY,GAClB,SAASF,EAAepF,EAAOC,EAASgB,GACvChB,EAAUA,GAAW,GAOrB,IAGIsF,EAHEC,EAAsBxD,EAAO,IAwBnC,OAvBAA,EAAO,KAAiB,EAavBuD,EATAtE,IACCA,EAAKI,QACLJ,EAAKyD,cACLzD,EAAK6C,gBACL7C,EAAKU,SACLV,EAAKgD,eACLhD,EAAKoD,KACLpD,EAAKkD,eAEAnD,EAAsBhB,EAAOC,EAASgB,GAEtCwE,EAAgBzF,EAAOC,GAAS,OAAOb,GAK1C4C,EAAO,KAAUA,EAAO,IAAShC,EAAOsF,GAC5CtD,EAAO,IAAiBwD,EACxBF,EAAUnH,OAAS,EACZoH,EAmER,SAASG,EAAkBrC,EAAMlC,GAChC,MAAa,cAATkC,EACI,QACY,YAATA,EACH,MACY,iBAATA,EACH,QACY,mBAATA,EACH,UACY,oBAATA,EACH,WACGlC,GAAazD,EAAMI,KAAKuF,GAC3BA,EAAKlE,cAAcT,QAAQ,WAAY,UAGxC2E,EAGR,SAASsC,EAAmBtC,EAAMW,GACjC,MAAa,UAATX,GAAyB,MAALW,GAA0B,iBAANA,EACpChF,EAAcgF,GACC,MAAZX,EAAK,IAA0B,MAAZA,EAAK,IAA2B,kBAANW,EAGhDvF,OAAOuF,GAGRA,EAGR,IAAMvE,EAAUD,MAAMC,QAChBoD,EAASD,OAAOC,OAGtB,SAAS4C,EAAgBzF,EAAOC,EAASkB,EAAWC,GAEnD,GAAa,MAATpB,IAA2B,IAAVA,IAA4B,IAAVA,GAA6B,KAAVA,EACzD,MAAO,GAIR,GAAqB,iBAAVA,EACV,OAAOpC,EAAeoC,GAIvB,GAAIP,EAAQO,GAAQ,CAEnB,IADA,IAAIuB,EAAW,GACNvD,EAAI,EAAGA,EAAIgC,EAAM7B,OAAQH,IACjCuD,GACYkE,EAAgBzF,EAAMhC,GAAIiC,EAASkB,EAAWC,GAE3D,OAAOG,EAGJS,EAAO,KAAQA,EAAO,IAAOhC,GAEjC,IAAIyB,EAAOzB,EAAMyB,KAChBtB,EAAQH,EAAMG,MAIf,GADoC,mBAATsB,EACV,CAChB,GAAIA,IAASI,EACZ,OAAO4D,EACNzF,EAAMG,MAAMZ,SACZU,EACAkB,EACAC,GAIF,IAAIG,EAEHA,EADGE,EAAKW,WAA8C,mBAA1BX,EAAKW,UAAUC,OA/G9C,SAA8BrC,EAAOC,GACpC,IAAIO,EAAWR,EAAMyB,KACpBa,EAAO/B,EAAWC,EAAUP,GAGzB8B,EAAI,IAAIvB,EAASR,EAAMG,MAAOmC,GAClCtC,EAAK,IAAc+B,EACnBA,EAAC,IAAU/B,EAEX+B,EAAC,KAAU,EACXA,EAAE5B,MAAQH,EAAMG,MACD,MAAX4B,EAAES,QAAeT,EAAES,MAAQ,IAEV,MAAjBT,EAAC,MACJA,EAAC,IAAeA,EAAES,OAGnBT,EAAE9B,QAAUqC,EACR9B,EAASmC,yBACZZ,EAAES,MAAQK,EACT,GACAd,EAAES,MACFhC,EAASmC,yBAAyBZ,EAAE5B,MAAO4B,EAAES,QAEpCT,EAAEe,qBACZf,EAAEe,qBAIFf,EAAES,MAAQT,EAAC,MAAiBA,EAAES,MAAQT,EAAC,IAAeA,EAAES,OAGzD,IAAIN,EAAaF,EAAO,IAGxB,OAFIE,GAAYA,EAAWlC,GAEpB+B,EAAEM,OAAON,EAAE5B,MAAO4B,EAAES,MAAOT,EAAE9B,SA6EvB2F,CAAqB5F,EAAOC,GA1I1C,SAAiCD,EAAOC,GACvC,IAAIsB,EACHQ,EAAIhC,EAAgBC,EAAOC,GAC3BqC,EAAO/B,EAAWP,EAAMyB,KAAMxB,GAE/BD,EAAK,IAAc+B,EASnB,IAFA,IAAIG,EAAaF,EAAO,IACpBe,EAAQ,EACLhB,EAAC,KAAWgB,IAAU,IAC5BhB,EAAC,KAAU,EAEPG,GAAYA,EAAWlC,GAG3BuB,EAAWvB,EAAMyB,KAAKuB,KAAKjB,EAAG/B,EAAMG,MAAOmC,GAG5C,OAAOf,EAqHMsE,CAAwB7F,EAAOC,GAG3C,IAAIuB,EAAYxB,EAAK,IACjBwB,EAAUyB,kBACbhD,EAAU4C,EAAO,GAAI5C,EAASuB,EAAUyB,oBAIzC,IAAMpF,EAAM4H,EAAgBlE,EAAUtB,EAASkB,EAAWC,GAE1D,OADIY,EAAO,QAAUA,EAAO,OAAShC,GAC9BnC,EAIR,IACC0B,EACAoE,EAFGpF,EAAI,IAMR,GAFAA,GAAQkD,EAEJtB,EAEH,IAAK,IAAIkD,KADT9D,EAAWY,EAAMZ,SACAY,EAAO,CACvB,IAAI6D,EAAI7D,EAAMkD,GAEd,KACU,QAATA,GACS,QAATA,GACS,WAATA,GACS,aAATA,GACS,aAATA,GACU,cAATA,GAAwB,UAAWlD,GAC1B,YAATkD,GAAsB,QAASlD,GAK7B1C,EAAYK,KAAKuF,IAKrB,GAFAW,EAAI2B,EADJtC,EAAOqC,EAAkBrC,EAAMlC,GACF6C,GAEhB,4BAATX,EACHM,EAAOK,GAAKA,EAAEI,eACK,aAAT3C,GAAgC,UAAT4B,EAEjC9D,EAAWyE,WACAA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,IAAU,IAANA,GAAoB,KAANA,EAAU,CAC3BA,EAAIX,EACJ9E,EAAIA,EAAI,IAAM8E,EACd,SAGD,GAAa,UAATA,EAAkB,CACrB,GAAa,WAAT5B,EAAmB,CACtBL,EAAc4C,EACd,SAGS,WAATvC,GACAL,GAAe4C,GAEb,aAAc7D,IAEhB5B,GAAQ,aAGVA,EAAIA,EAAI,IAAM8E,EAAO,KAAOzF,EAAeoG,GAAK,KAKnD,IAAI8B,EAAevH,EAGnB,GAFAA,GAAQ,IAEJd,EAAYK,KAAK2D,GACpB,UAAU+C,MAAS/C,sCAAwClD,GAG5D,IAAIoG,EAAS,GACToB,GAAc,EAElB,GAAIpC,EACHgB,GAAkBhB,EAClBoC,GAAc,UACgB,iBAAbxG,EACjBoF,GAAkB/G,EAAe2B,GACjCwG,GAAc,UACJtG,EAAQF,GAClB,IAAK,IAAIvB,EAAI,EAAGA,EAAIuB,EAASpB,OAAQH,IAAK,CACzC,IAAI8G,EAAQvF,EAASvB,GAErB,GAAa,MAAT8G,IAA2B,IAAVA,EAAiB,CACrC,IAEIC,EAAMU,EAAgBX,EAAO7E,EADvB,QAATwB,GAA4B,kBAATA,GAA4BN,EACQC,GAGpD2D,IACHJ,GAAkBI,EAClBgB,GAAc,YAIK,MAAZxG,IAAiC,IAAbA,IAAmC,IAAbA,EAAmB,CACvE,IAEIwF,EAAMU,EAAgBlG,EAAUU,EAD1B,QAATwB,GAA4B,kBAATA,GAA4BN,EACWC,GAGvD2D,IACHJ,GAAkBI,EAClBgB,GAAc,GAMhB,GAFI/D,EAAO,QAAUA,EAAO,OAAShC,GAEjC+F,EACHxH,GAAQoG,UACEnH,EAAcM,KAAK2D,GAC7B,OAAOqE,EAAe,MAGvB,OAAOvH,EAAI,KAAOkD,EAAO,IAK1B2D,EAAeC,cAAgBA"}