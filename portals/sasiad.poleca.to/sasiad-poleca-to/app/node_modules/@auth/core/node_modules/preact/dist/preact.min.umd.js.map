{"version": 3, "file": "preact.min.umd.js", "sources": ["../src/util.js", "../src/options.js", "../src/create-element.js", "../src/component.js", "../src/create-context.js", "../src/constants.js", "../src/diff/children.js", "../src/diff/props.js", "../src/diff/index.js", "../src/render.js", "../src/diff/catch-error.js", "../src/clone-element.js", "../src/cjs.js"], "sourcesContent": ["import { EMPTY_ARR } from \"./constants\";\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-ignore We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {Node} node The node to remove\n */\nexport function removeNode(node) {\n\tlet parentNode = node.parentNode;\n\tif (parentNode) parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * constructor for this virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\t_hydrating: null,\n\t\tconstructor: undefined,\n\t\t_original: original == null ? ++vnodeId : original\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == null && options.vnode != null) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is import('./internal').VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor === undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function Component(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nComponent.prototype.setState = function(update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != null && this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {import('./index').ComponentChildren | void}\n */\nComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet vnode = component._vnode,\n\t\toldDom = vnode._dom,\n\t\tparentDom = component._parentDom;\n\n\tif (parentDom) {\n\t\tlet commitQueue = [];\n\t\tconst oldVNode = assign({}, vnode);\n\t\toldVNode._original = vnode._original + 1;\n\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tvnode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tparentDom.ownerSVGElement !== undefined,\n\t\t\tvnode._hydrating != null ? [oldDom] : null,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom,\n\t\t\tvnode._hydrating\n\t\t);\n\t\tcommitRoot(commitQueue, vnode);\n\n\t\tif (vnode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(vnode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || setTimeout)(process);\n\t}\n}\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet queue;\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\n\t\trerenderQueue = [];\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\n\t\tqueue.some(c => {\n\t\t\tif (c._dirty) renderComponent(c);\n\t\t});\n\t}\n}\n\nprocess._rerenderCount = 0;\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue, contextId) {\n\tcontextId = '__cC' + i++;\n\n\tconst context = {\n\t\t_id: contextId,\n\t\t_defaultValue: defaultValue,\n\t\t/** @type {import('./internal').FunctionComponent} */\n\t\tConsumer(props, contextValue) {\n\t\t\t// return props.children(\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\n\t\t\t// );\n\t\t\treturn props.children(contextValue);\n\t\t},\n\t\t/** @type {import('./internal').FunctionComponent} */\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\tlet subs = [];\n\t\t\t\tlet ctx = {};\n\t\t\t\tctx[contextId] = this;\n\n\t\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\t\tthis.shouldComponentUpdate = function(_props) {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\t// I think the forced value propagation here was only needed when `options.debounceRendering` was being bypassed:\n\t\t\t\t\t\t// https://github.com/preactjs/preact/commit/4d339fb803bea09e9f198abf38ca1bf8ea4b7771#diff-54682ce380935a717e41b8bfc54737f6R358\n\t\t\t\t\t\t// In those cases though, even with the value corrected, we're double-rendering all nodes.\n\t\t\t\t\t\t// It might be better to just tell folks not to use force-sync mode.\n\t\t\t\t\t\t// Currently, using `useContext()` in a class component will overwrite its `this.context` value.\n\t\t\t\t\t\t// subs.some(c => {\n\t\t\t\t\t\t// \tc.context = _props.value;\n\t\t\t\t\t\t// \tenqueueRender(c);\n\t\t\t\t\t\t// });\n\n\t\t\t\t\t\t// subs.some(c => {\n\t\t\t\t\t\t// \tc.context[contextId] = _props.value;\n\t\t\t\t\t\t// \tenqueueRender(c);\n\t\t\t\t\t\t// });\n\t\t\t\t\t\tsubs.some(enqueueRender);\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.push(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\n\t\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\n\treturn (context.Provider._contextRef = context.Consumer.contextType = context);\n}\n", "export const EMPTY_OBJ = {};\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\n * children are being diffed\n * @param {import('../internal').ComponentChildren[]} renderResult\n * @param {import('../internal').VNode} newParentVNode The new virtual\n * node whose children should be diff'ed against oldParentVNode\n * @param {import('../internal').VNode} oldParentVNode The old virtual\n * node whose children should be diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by getChildContext\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet oldChildrenLength = oldChildren.length;\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < renderResult.length; i++) {\n\t\tchildVNode = renderResult[i];\n\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint'\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t} else if (Array.isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : null,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\t// Terser removes the `continue` here and wraps the loop body\n\t\t// in a `if (childVNode) { ... } condition\n\t\tif (childVNode == null) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Check if we find a corresponding element in oldChildren.\n\t\t// If found, delete the array item by setting to `undefined`.\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\n\t\t// (holes).\n\t\toldVNode = oldChildren[i];\n\n\t\tif (\n\t\t\toldVNode === null ||\n\t\t\t(oldVNode &&\n\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\tchildVNode.type === oldVNode.type)\n\t\t) {\n\t\t\toldChildren[i] = undefined;\n\t\t} else {\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\n\t\t\t\toldVNode = oldChildren[j];\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\n\t\t\t\t// We always match by type (in either case).\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\t\tchildVNode.type === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\toldChildren[j] = undefined;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\toldVNode = null;\n\t\t\t}\n\t\t}\n\n\t\toldVNode = oldVNode || EMPTY_OBJ;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tisSvg,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating\n\t\t);\n\n\t\tnewDom = childVNode._dom;\n\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\n\t\t\tif (!refs) refs = [];\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\n\t\t}\n\n\t\tif (newDom != null) {\n\t\t\tif (firstChildDom == null) {\n\t\t\t\tfirstChildDom = newDom;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\ttypeof childVNode.type == 'function' &&\n\t\t\t\tchildVNode._children === oldVNode._children\n\t\t\t) {\n\t\t\t\tchildVNode._nextDom = oldDom = reorderChildren(\n\t\t\t\t\tchildVNode,\n\t\t\t\t\toldDom,\n\t\t\t\t\tparentDom\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\toldDom = placeChild(\n\t\t\t\t\tparentDom,\n\t\t\t\t\tchildVNode,\n\t\t\t\t\toldVNode,\n\t\t\t\t\toldChildren,\n\t\t\t\t\tnewDom,\n\t\t\t\t\toldDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (typeof newParentVNode.type == 'function') {\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\n\t\t\t\t//\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t\t\t\t// node's nextSibling.\n\t\t\t\tnewParentVNode._nextDom = oldDom;\n\t\t\t}\n\t\t} else if (\n\t\t\toldDom &&\n\t\t\toldVNode._dom == oldDom &&\n\t\t\toldDom.parentNode != parentDom\n\t\t) {\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\n\t\t\toldDom = getDomSibling(oldVNode);\n\t\t}\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\t// Remove remaining oldChildren if there are any.\n\tfor (i = oldChildrenLength; i--; ) {\n\t\tif (oldChildren[i] != null) {\n\t\t\tunmount(oldChildren[i], oldChildren[i]);\n\t\t}\n\t}\n\n\t// Set refs only after unmount\n\tif (refs) {\n\t\tfor (i = 0; i < refs.length; i++) {\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\n\t\t}\n\t}\n}\n\nfunction reorderChildren(childVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\tlet c = childVNode._children;\n\tlet tmp = 0;\n\tfor (; c && tmp < c.length; tmp++) {\n\t\tlet vnode = c[tmp];\n\t\tif (vnode) {\n\t\t\t// We typically enter this code path on sCU bailout, where we copy\n\t\t\t// oldVNode._children to newVNode._children. If that is the case, we need\n\t\t\t// to update the old children's _parent pointer to point to the newVNode\n\t\t\t// (childVNode here).\n\t\t\tvnode._parent = childVNode;\n\n\t\t\tif (typeof vnode.type == 'function') {\n\t\t\t\toldDom = reorderChildren(vnode, oldDom, parentDom);\n\t\t\t} else {\n\t\t\t\toldDom = placeChild(parentDom, vnode, vnode, c, vnode._dom, oldDom);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {import('../index').ComponentChildren} children The unflattened\n * children of a virtual node\n * @returns {import('../internal').VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == null || typeof children == 'boolean') {\n\t} else if (Array.isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\nfunction placeChild(\n\tparentDom,\n\tchildVNode,\n\toldVNode,\n\toldChildren,\n\tnewDom,\n\toldDom\n) {\n\tlet nextDom;\n\tif (childVNode._nextDom !== undefined) {\n\t\t// Only Fragments or components that return Fragment like VNodes will\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\n\t\t// of last DOM child of this child VNode\n\t\tnextDom = childVNode._nextDom;\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\n\t\t// can clean up the property\n\t\tchildVNode._nextDom = undefined;\n\t} else if (\n\t\toldVNode == null ||\n\t\tnewDom != oldDom ||\n\t\tnewDom.parentNode == null\n\t) {\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\n\t\t\tparentDom.appendChild(newDom);\n\t\t\tnextDom = null;\n\t\t} else {\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\n\t\t\tfor (\n\t\t\t\tlet sibDom = oldDom, j = 0;\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\n\t\t\t\tj += 1\n\t\t\t) {\n\t\t\t\tif (sibDom == newDom) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparentDom.insertBefore(newDom, oldDom);\n\t\t\tnextDom = oldDom;\n\t\t}\n\t}\n\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\n\t// See more detail in create-element.js:createVNode\n\tif (nextDom !== undefined) {\n\t\toldDom = nextDom;\n\t} else {\n\t\toldDom = newDom.nextSibling;\n\t}\n\n\treturn oldDom;\n}\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\n/**\n * Diff the old and new properties of a VNode and apply changes to the DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to apply\n * changes to\n * @param {object} newProps The new props\n * @param {object} oldProps The old props\n * @param {boolean} isSvg Whether or not this node is an SVG node\n * @param {boolean} hydrate Whether or not we are in hydration mode\n */\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\n\tlet i;\n\n\tfor (i in oldProps) {\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\n\t\t}\n\t}\n\n\tfor (i in newProps) {\n\t\tif (\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\n\t\t\ti !== 'children' &&\n\t\t\ti !== 'key' &&\n\t\t\ti !== 'value' &&\n\t\t\ti !== 'checked' &&\n\t\t\toldProps[i] !== newProps[i]\n\t\t) {\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\n\t\t}\n\t}\n}\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value);\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, isSvg) {\n\tlet useCapture;\n\n\to: if (name === 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (name.toLowerCase() in dom) name = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\n\t\t\t\tdom.addEventListener(name, handler, useCapture);\n\t\t\t}\n\t\t} else {\n\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\n\t\t\tdom.removeEventListener(name, handler, useCapture);\n\t\t}\n\t} else if (name !== 'dangerouslySetInnerHTML') {\n\t\tif (isSvg) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname !== 'href' &&\n\t\t\tname !== 'list' &&\n\t\t\tname !== 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname !== 'tabIndex' &&\n\t\t\tname !== 'download' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == null ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// ARIA-attributes have a different notion of boolean values.\n\t\t// The value `false` is different from the attribute not\n\t\t// existing on the DOM, so we can't remove it. For non-boolean\n\t\t// ARIA-attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost us too many bytes. On top of\n\t\t// that other VDOM frameworks also always stringify `false`.\n\n\t\tif (typeof value === 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != null && (value !== false || name.indexOf('-') != -1)) {\n\t\t\tdom.setAttribute(name, value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Proxy an event to hooked event handlers\n * @param {Event} e The event object from the browser\n * @private\n */\nfunction eventProxy(e) {\n\tthis._listeners[e.type + false](options.event ? options.event(e) : e);\n}\n\nfunction eventProxyCapture(e) {\n\tthis._listeners[e.type + true](options.event ? options.event(e) : e);\n}\n", "import { EMPTY_OBJ } from '../constants';\nimport { Component, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { diffProps, setProperty } from './props';\nimport { assign, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by getChildContext\n * @param {boolean} isSvg Whether or not this element is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} [isHydrating] Whether or not we are in hydration\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._hydrating != null) {\n\t\tisHydrating = oldVNode._hydrating;\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\t// if we resume, we want the tree to be \"unlocked\"\n\t\tnewVNode._hydrating = null;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\ttry {\n\t\touter: if (typeof newType == 'function') {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\t\t// @ts-ignore The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-ignore Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original === oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\tc.props = newProps;\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\n\t\t\t\t\tc._vnode = newVNode;\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.forEach(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._vnode = newVNode;\n\t\t\tc._parentDom = parentDom;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._hydrating = null;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\n\t\t\tc._force = false;\n\t\t} else if (\n\t\t\texcessDomChildren == null &&\n\t\t\tnewVNode._original === oldVNode._original\n\t\t) {\n\t\t\tnewVNode._children = oldVNode._children;\n\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t} else {\n\t\t\tnewVNode._dom = diffElementNodes(\n\t\t\t\toldVNode._dom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\n\t} catch (e) {\n\t\tnewVNode._original = null;\n\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\tif (isHydrating || excessDomChildren != null) {\n\t\t\tnewVNode._dom = oldDom;\n\t\t\tnewVNode._hydrating = !!isHydrating;\n\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n\t\t\t// ^ could possibly be simplified to:\n\t\t\t// excessDomChildren.length = 0;\n\t\t}\n\t\toptions._catchError(e, newVNode, oldVNode);\n\t}\n}\n\n/**\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').VNode} root\n */\nexport function commitRoot(commitQueue, root) {\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-ignore Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-ignore See above ts-ignore on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {import('../internal').PreactElement} dom The DOM element representing\n * the virtual nodes being diffed\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {*} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @returns {import('../internal').PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = newVNode.type;\n\tlet i = 0;\n\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\n\tif (nodeType === 'svg') isSvg = true;\n\n\tif (excessDomChildren != null) {\n\t\tfor (; i < excessDomChildren.length; i++) {\n\t\t\tconst child = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tchild &&\n\t\t\t\t'setAttribute' in child === !!nodeType &&\n\t\t\t\t(nodeType ? child.localName === nodeType : child.nodeType === 3)\n\t\t\t) {\n\t\t\t\tdom = child;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (nodeType === null) {\n\t\t\t// @ts-ignore createTextNode returns Text, we expect PreactElement\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tif (isSvg) {\n\t\t\tdom = document.createElementNS(\n\t\t\t\t'http://www.w3.org/2000/svg',\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\n\t\t\t\tnodeType\n\t\t\t);\n\t\t} else {\n\t\t\tdom = document.createElement(\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\n\t\t\t\tnodeType,\n\t\t\t\tnewProps.is && newProps\n\t\t\t);\n\t\t}\n\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\n\t\tisHydrating = false;\n\t}\n\n\tif (nodeType === null) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tif (!isHydrating) {\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\n\t\t\t// we should read the existing DOM attributes to diff them\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\toldProps = {};\n\t\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (newHtml || oldHtml) {\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\t\tif (\n\t\t\t\t\t!newHtml ||\n\t\t\t\t\t((!oldHtml || newHtml.__html != oldHtml.__html) &&\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML)\n\t\t\t\t) {\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\ti = newVNode.props.children;\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tArray.isArray(i) ? i : [i],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg && nodeType !== 'foreignObject',\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// (as above, don't diff props during hydration)\n\t\tif (!isHydrating) {\n\t\t\tif (\n\t\t\t\t'value' in newProps &&\n\t\t\t\t(i = newProps.value) !== undefined &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(i !== dom.value ||\n\t\t\t\t\t(nodeType === 'progress' && !i) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType === 'option' && i !== oldProps.value))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\n\t\t\t}\n\t\t\tif (\n\t\t\t\t'checked' in newProps &&\n\t\t\t\t(i = newProps.checked) !== undefined &&\n\t\t\t\ti !== dom.checked\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {object|function} ref\n * @param {any} value\n * @param {import('../internal').VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') ref(value);\n\t\telse ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {import('../internal').VNode} vnode The virtual node to unmount\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\n * initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) {\n\t\t\tapplyRef(r, null, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t\tvnode._component = undefined;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type !== 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove && vnode._dom != null) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._parent = vnode._dom = vnode._nextDom = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode === 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = (\n\t\t(!isHydrating && replaceNode) ||\n\t\tparentDom\n\t)._children = createElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.ownerSVGElement !== undefined,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t? null\n\t\t\t: parentDom.firstChild\n\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t: null,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t? oldVNode._dom\n\t\t\t: parentDom.firstChild,\n\t\tisHydrating\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw\n * the error that was caught (except for unmounting when this parameter\n * is the highest parent that was being unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component, ctor, handled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tnull\n\t);\n}\n", "import * as preact from './index.js';\nif (typeof module < 'u') module.exports = preact;\nelse self.preact = preact;\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__d", "__c", "__h", "constructor", "__v", "Fragment", "Component", "context", "this", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "debounceRendering", "setTimeout", "queue", "__r", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "oldDom", "parentDom", "__P", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "newDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "reorderC<PERSON>dren", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "applyRef", "tmp", "nextDom", "sibDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "diffProps", "dom", "newProps", "oldProps", "hydrate", "setProperty", "setStyle", "style", "value", "test", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "l", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "removeAttribute", "setAttribute", "event", "newVNode", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "renderHook", "count", "newType", "contextType", "__E", "prototype", "render", "doR<PERSON>", "sub", "state", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "for<PERSON>ach", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "diffElementNodes", "diffed", "root", "cb", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "__html", "innerHTML", "checked", "current", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "_props", "old", "splice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "module", "exports", "preact", "self"], "mappings": "iFA0BaA,ECfPC,ECRFC,EA6FSC,EC4ETC,EAWAC,ECrLOC,ICFEC,EAAY,CAAlB,EACMC,EAAY,GACZC,EAAqB,oELOlBC,SAAAA,EAAOC,EAAKC,GAE3B,IAAK,IAAIN,KAAKM,EAAOD,EAAIL,GAAKM,EAAMN,GACpC,OAA6BK,CAC7B,CAQM,SAASE,EAAWC,GAC1B,IAAIC,EAAaD,EAAKC,WAClBA,GAAYA,EAAWC,YAAYF,EACvC,CEXM,SAASG,EAAcC,EAAMN,EAAOO,GAC1C,IACCC,EACAC,EACAf,EAHGgB,EAAkB,CAAA,EAItB,IAAKhB,KAAKM,EACA,OAALN,EAAYc,EAAMR,EAAMN,GACd,OAALA,EAAYe,EAAMT,EAAMN,GAC5BgB,EAAgBhB,GAAKM,EAAMN,GAUjC,GAPIiB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIxB,EAAMyB,KAAKF,UAAW,GAAKJ,GAKjC,mBAARD,GAA2C,MAArBA,EAAKQ,aACrC,IAAKpB,KAAKY,EAAKQ,kBACaC,IAAvBL,EAAgBhB,KACnBgB,EAAgBhB,GAAKY,EAAKQ,aAAapB,IAK1C,OAAOsB,EAAYV,EAAMI,EAAiBF,EAAKC,EAAK,KACpD,UAceO,EAAYV,EAAMN,EAAOQ,EAAKC,EAAKQ,GAGlD,IAAMC,EAAQ,CACbZ,KAAAA,EACAN,MAAAA,EACAQ,IAAAA,EACAC,IAAAA,EACAU,IAAW,KACXC,GAAS,KACTC,IAAQ,EACRC,IAAM,KAKNC,SAAUR,EACVS,IAAY,KACZC,IAAY,KACZC,iBAAaX,EACbY,IAAuB,MAAZV,IAAqB3B,EAAU2B,GAM3C,OAFgB,MAAZA,GAAqC,MAAjB5B,EAAQ6B,OAAe7B,EAAQ6B,MAAMA,GAEtDA,CACP,CAMM,SAASU,EAAS5B,GACxB,OAAOA,EAAMO,QACb,UC7EesB,EAAU7B,EAAO8B,GAChCC,KAAK/B,MAAQA,EACb+B,KAAKD,QAAUA,CACf,UA0EeE,EAAcd,EAAOe,GACpC,GAAkB,MAAdA,EAEH,OAAOf,EAAKE,GACTY,EAAcd,EAADE,GAAgBF,EAAAE,GAAAD,IAAwBe,QAAQhB,GAAS,GACtE,KAIJ,IADA,IAAIiB,EACGF,EAAaf,EAAAC,IAAgBP,OAAQqB,IAG3C,GAAe,OAFfE,EAAUjB,EAAKC,IAAWc,KAEa,MAAhBE,EAAAb,IAItB,OAAOa,EACPb,IAQF,MAA4B,mBAAdJ,EAAMZ,KAAqB0B,EAAcd,GAAS,IAChE,CAsCD,SAASkB,EAAwBlB,GAAjC,IAGWxB,EACJ2C,EAHN,GAA+B,OAA1BnB,EAAQA,EAAHE,KAAiD,MAApBF,EAAKM,IAAqB,CAEhE,IADAN,MAAaA,EAAAM,IAAiBc,KAAO,KAC5B5C,EAAI,EAAGA,EAAIwB,MAAgBN,OAAQlB,IAE3C,GAAa,OADT2C,EAAQnB,EAAAC,IAAgBzB,KACO,MAAd2C,EAAKf,IAAe,CACxCJ,EAAAI,IAAaJ,EAAKM,IAAYc,KAAOD,EAAxBf,IACb,KACA,CAGF,OAAOc,EAAwBlB,EAC/B,CACD,CAuBM,SAASqB,EAAcC,KAE1BA,QACAA,EAACjB,KAAU,IACZ/B,EAAciD,KAAKD,KAClBE,SACFjD,IAAiBJ,EAAQsD,sBAEzBlD,EAAeJ,EAAQsD,oBACNC,YAAYF,EAE9B,CAGD,SAASA,IAER,IADA,IAAIG,EACIH,EAAOI,IAAkBtD,EAAcoB,QAC9CiC,EAAQrD,EAAcuD,KAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAArB,IAAAN,IAAkB4B,EAA5BtB,IAAAN,GAAA,GAC3B7B,EAAgB,GAGhBqD,EAAMK,KAAK,SAAAV,GAzFb,IAAyBW,EAMnBC,EACEC,EANHnC,EACHoC,EACAC,EAuFKf,EAAJjB,MAxFD+B,GADGpC,GADoBiC,EA0FQX,GAzFhCb,KAAAL,KAECiC,EAAYJ,EAFbK,OAKKJ,EAAc,IACZC,EAAWvD,EAAO,CAAA,EAAIoB,IAC5BS,IAAqBT,EAAKS,IAAa,EAEvC8B,EACCF,EACArC,EACAmC,EACAF,WAC8BpC,IAA9BwC,EAAUG,gBACU,MAApBxC,EAAKO,IAAsB,CAAC6B,GAAU,KACtCF,EACU,MAAVE,EAAiBtB,EAAcd,GAASoC,EACxCpC,EATDO,KAWAkC,EAAWP,EAAalC,GAEpBA,EAAKI,KAASgC,GACjBlB,EAAwBlB,IAmExB,EAEF,UG7Le0C,EACfL,EACAM,EACAC,EACAC,EACAC,EACAC,EACAC,EACAd,EACAE,EACAa,OAEIzE,EAAG0E,EAAGf,EAAUgB,EAAYC,EAAQC,EAAeC,EAInDC,EAAeV,GAAkBA,EAAJ5C,KAAiCvB,EAE9D8E,EAAoBD,EAAY7D,OAGpC,IADAkD,MAA2B,GACtBpE,EAAI,EAAGA,EAAImE,EAAajD,OAAQlB,IAgDpC,GAAkB,OA5CjB2E,EAAaP,EAAc3C,IAAWzB,GADrB,OAFlB2E,EAAaR,EAAanE,KAEqB,kBAAd2E,EACW,KAMtB,iBAAdA,GACc,iBAAdA,GAEc,iBAAdA,EAEoCrD,EAC1C,KACAqD,EACA,KACA,KACAA,GAESM,MAAMC,QAAQP,GACmBrD,EAC1CY,EACA,CAAErB,SAAU8D,GACZ,KACA,KACA,MAESA,EAAAhD,IAAoB,EAKaL,EAC1CqD,EAAW/D,KACX+D,EAAWrE,MACXqE,EAAW7D,IACX6D,EAAW5D,IAAM4D,EAAW5D,IAAM,KAClC4D,EAED1C,KAC2C0C,GAK5C,CAaA,GATAA,EAAAjD,GAAqB0C,EACrBO,EAAUhD,IAAUyC,EAAAzC,IAAwB,EAS9B,QAHdgC,EAAWoB,EAAY/E,KAIrB2D,GACAgB,EAAW7D,KAAO6C,EAAS7C,KAC3B6D,EAAW/D,OAAS+C,EAAS/C,KAE9BmE,EAAY/E,QAAKqB,OAIjB,IAAKqD,EAAI,EAAGA,EAAIM,EAAmBN,IAAK,CAIvC,IAHAf,EAAWoB,EAAYL,KAKtBC,EAAW7D,KAAO6C,EAAS7C,KAC3B6D,EAAW/D,OAAS+C,EAAS/C,KAC5B,CACDmE,EAAYL,QAAKrD,EACjB,KACA,CACDsC,EAAW,IACX,CAMFI,EACCF,EACAc,EALDhB,EAAWA,GAAY1D,EAOtBqE,EACAC,EACAC,EACAd,EACAE,EACAa,GAGDG,EAASD,EAAT/C,KAEK8C,EAAIC,EAAW5D,MAAQ4C,EAAS5C,KAAO2D,IACtCI,IAAMA,EAAO,IACdnB,EAAS5C,KAAK+D,EAAK/B,KAAKY,EAAS5C,IAAK,KAAM4D,GAChDG,EAAK/B,KAAK2B,EAAGC,EAAA7C,KAAyB8C,EAAQD,IAGjC,MAAVC,GACkB,MAAjBC,IACHA,EAAgBD,GAIU,mBAAnBD,EAAW/D,MAClB+D,EAAAlD,MAAyBkC,EAAzBlC,IAEAkD,EAAU9C,IAAY+B,EAASuB,EAC9BR,EACAf,EACAC,GAGDD,EAASwB,EACRvB,EACAc,EACAhB,EACAoB,EACAH,EACAhB,GAIgC,mBAAvBQ,EAAexD,OAQzBwD,EAAAvC,IAA0B+B,IAG3BA,GACAD,EAAQ/B,KAASgC,GACjBA,EAAOnD,YAAcoD,IAIrBD,EAAStB,EAAcqB,GAtGvB,CA6GF,IAHAS,EAAAxC,IAAsBiD,EAGjB7E,EAAIgF,EAAmBhF,KACL,MAAlB+E,EAAY/E,IACfqF,EAAQN,EAAY/E,GAAI+E,EAAY/E,IAKtC,GAAI8E,EACH,IAAK9E,EAAI,EAAGA,EAAI8E,EAAK5D,OAAQlB,IAC5BsF,EAASR,EAAK9E,GAAI8E,IAAO9E,GAAI8E,IAAO9E,GAGtC,CAED,SAASmF,EAAgBR,EAAYf,EAAQC,GAI5C,IAJD,IAKMrC,EAHDsB,EAAI6B,EAAHlD,IACD8D,EAAM,EACHzC,GAAKyC,EAAMzC,EAAE5B,OAAQqE,KACvB/D,EAAQsB,EAAEyC,MAMb/D,KAAgBmD,EAGff,EADwB,mBAAdpC,EAAMZ,KACPuE,EAAgB3D,EAAOoC,EAAQC,GAE/BuB,EAAWvB,EAAWrC,EAAOA,EAAOsB,EAAGtB,EAAYoC,IAAAA,IAK/D,OAAOA,CACP,CAqBD,SAASwB,EACRvB,EACAc,EACAhB,EACAoB,EACAH,EACAhB,GAND,IAQK4B,EAuBGC,EAAiBf,EAtBxB,QAA4BrD,IAAxBsD,EAAU9C,IAIb2D,EAAUb,EAAH9C,IAMP8C,WAAsBtD,OAEtBsC,GAAY,MAAZA,GACAiB,GAAUhB,GACW,MAArBgB,EAAOnE,WAEPiF,EAAO,GAAc,MAAV9B,GAAkBA,EAAOnD,aAAeoD,EAClDA,EAAU8B,YAAYf,GACtBY,EAAU,SACJ,CAEN,IACKC,EAAS7B,EAAQc,EAAI,GACxBe,EAASA,EAAOG,cAAgBlB,EAAIK,EAAY7D,OACjDwD,GAAK,EAEL,GAAIe,GAAUb,EACb,MAAMc,EAGR7B,EAAUgC,aAAajB,EAAQhB,GAC/B4B,EAAU5B,CACV,CAYF,YANgBvC,IAAZmE,EACMA,EAEAZ,EAAOgB,WAIjB,CChTeE,SAAAA,EAAUC,EAAKC,EAAUC,EAAU1B,EAAO2B,GACzD,IAAIlG,EAEJ,IAAKA,KAAKiG,EACC,aAANjG,GAA0B,QAANA,GAAiBA,KAAKgG,GAC7CG,EAAYJ,EAAK/F,EAAG,KAAMiG,EAASjG,GAAIuE,GAIzC,IAAKvE,KAAKgG,EAENE,GAAiC,mBAAfF,EAAShG,IACvB,aAANA,GACM,QAANA,GACM,UAANA,GACM,YAANA,GACAiG,EAASjG,KAAOgG,EAAShG,IAEzBmG,EAAYJ,EAAK/F,EAAGgG,EAAShG,GAAIiG,EAASjG,GAAIuE,EAGhD,CAED,SAAS6B,EAASC,EAAOvF,EAAKwF,GACd,MAAXxF,EAAI,GACPuF,EAAMF,YAAYrF,EAAKwF,GAEvBD,EAAMvF,GADa,MAATwF,EACG,GACa,iBAATA,GAAqBnG,EAAmBoG,KAAKzF,GACjDwF,EAEAA,EAAQ,IAEtB,UAUeH,EAAYJ,EAAKS,EAAMF,EAAOG,EAAUlC,OACnDmC,EAEJC,EAAG,GAAa,UAATH,EACN,GAAoB,iBAATF,EACVP,EAAIM,MAAMO,QAAUN,MACd,CAKN,GAJuB,iBAAZG,IACVV,EAAIM,MAAMO,QAAUH,EAAW,IAG5BA,EACH,IAAKD,KAAQC,EACNH,GAASE,KAAQF,GACtBF,EAASL,EAAIM,MAAOG,EAAM,IAK7B,GAAIF,EACH,IAAKE,KAAQF,EACPG,GAAYH,EAAME,KAAUC,EAASD,IACzCJ,EAASL,EAAIM,MAAOG,EAAMF,EAAME,GAInC,MAGOA,GAAY,MAAZA,EAAK,IAA0B,MAAZA,EAAK,GAChCE,EAAaF,KAAUA,EAAOA,EAAKK,QAAQ,WAAY,KAGxBL,EAA3BA,EAAKM,gBAAiBf,EAAYS,EAAKM,cAAcpH,MAAM,GACnD8G,EAAK9G,MAAM,GAElBqG,EAALgB,IAAqBhB,EAAAgB,EAAiB,CAAA,GACtChB,EAAAgB,EAAeP,EAAOE,GAAcJ,EAEhCA,EACEG,GAEJV,EAAIiB,iBAAiBR,EADLE,EAAaO,EAAoBC,EACbR,GAIrCX,EAAIoB,oBAAoBX,EADRE,EAAaO,EAAoBC,EACVR,WAErB,4BAATF,EAAoC,CAC9C,GAAIjC,EAIHiC,EAAOA,EAAKK,QAAQ,cAAe,KAAKA,QAAQ,SAAU,UACpD,GACG,SAATL,GACS,SAATA,GACS,SAATA,GAGS,aAATA,GACS,aAATA,GACAA,KAAQT,EAER,IACCA,EAAIS,GAAiB,MAATF,EAAgB,GAAKA,EAEjC,MAAMK,EACL,MAAOS,IAUW,mBAAVd,IAES,MAATA,IAA4B,IAAVA,IAAyC,GAAtBE,EAAKhE,QAAQ,KAG5DuD,EAAIsB,gBAAgBb,GAFpBT,EAAIuB,aAAad,EAAMF,GAIxB,CACD,CAOD,SAASY,EAAWE,GACnB/E,OAAgB+E,EAAExG,MAAO,GAAOjB,EAAQ4H,MAAQ5H,EAAQ4H,MAAMH,GAAKA,EACnE,CAED,SAASH,EAAkBG,GAC1B/E,KAAA0E,EAAgBK,EAAExG,MAAO,GAAMjB,EAAQ4H,MAAQ5H,EAAQ4H,MAAMH,GAAKA,EAClE,CClIerD,SAAAA,EACfF,EACA2D,EACA7D,EACAW,EACAC,EACAC,EACAd,EACAE,EACAa,GATeV,IAWXwB,EAoBEzC,EAAG2E,EAAOxB,EAAUyB,EAAUC,EAAUC,EACxC5B,EAKA6B,EACAC,EA6FO9H,EA4BP+H,EACHC,EASShI,EA6BNmE,EA1LL8D,EAAUT,EAAS5G,KAIpB,QAA6BS,IAAzBmG,EAASxF,YAA2B,OAAA,KAGb,MAAvB2B,EAAA5B,MACH0C,EAAcd,EAAH5B,IACX6B,EAAS4D,EAAA5F,IAAgB+B,EAAhB/B,IAET4F,EAAAzF,IAAsB,KACtByC,EAAoB,CAACZ,KAGjB2B,EAAM5F,QAAgB4F,EAAIiC,GAE/B,IACC9B,EAAO,GAAsB,mBAAXuC,EAAuB,CA4DxC,GA1DIjC,EAAWwB,EAASlH,MAKpBuH,GADJtC,EAAM0C,EAAQC,cACQ5D,EAAciB,EAApCzD,KACIgG,EAAmBvC,EACpBsC,EACCA,EAASvH,MAAMgG,MACff,EAHsB7D,GAIvB4C,EAGCX,EAAqB7B,IAExB8F,GADA9E,EAAI0E,EAAQ1F,IAAc6B,EAA1B7B,KAC4BJ,GAAwBoB,EACpDqF,KAEI,cAAeF,GAAWA,EAAQG,UAAUC,OAE/Cb,EAAQ1F,IAAcgB,EAAI,IAAImF,EAAQjC,EAAU8B,IAGhDN,EAAA1F,IAAsBgB,EAAI,IAAIX,EAAU6D,EAAU8B,GAClDhF,EAAEd,YAAciG,EAChBnF,EAAEuF,OAASC,GAERT,GAAUA,EAASU,IAAIzF,GAE3BA,EAAExC,MAAQ0F,EACLlD,EAAE0F,QAAO1F,EAAE0F,MAAQ,CAAV,GACd1F,EAAEV,QAAU0F,EACZhF,MAAmBwB,EACnBmD,EAAQ3E,EAAAjB,KAAW,EACnBiB,EAACf,IAAoB,GACrBe,EAAA2F,IAAoB,IAID,MAAhB3F,EAAA4F,MACH5F,EAAA4F,IAAe5F,EAAE0F,OAGsB,MAApCP,EAAQU,2BACP7F,EAAA4F,KAAgB5F,EAAE0F,QACrB1F,EAAA4F,IAAetI,EAAO,CAAD,EAAK0C,EAAL4F,MAGtBtI,EACC0C,EADK4F,IAELT,EAAQU,yBAAyB3C,EAAUlD,EAA3C4F,OAIFzC,EAAWnD,EAAExC,MACboH,EAAW5E,EAAE0F,MAGTf,EAEkC,MAApCQ,EAAQU,0BACgB,MAAxB7F,EAAE8F,oBAEF9F,EAAE8F,qBAGwB,MAAvB9F,EAAE+F,mBACL/F,EAACf,IAAkBgB,KAAKD,EAAE+F,uBAErB,CASN,GAPqC,MAApCZ,EAAQU,0BACR3C,IAAaC,GACkB,MAA/BnD,EAAEgG,2BAEFhG,EAAEgG,0BAA0B9C,EAAU8B,IAIpChF,EACDA,KAA2B,MAA3BA,EAAEiG,wBAKI,IAJNjG,EAAEiG,sBACD/C,EACAlD,EACAgF,IAAAA,IAEFN,EAAAvF,MAAuB0B,EAAvB1B,IACC,CAYD,IAXAa,EAAExC,MAAQ0F,EACVlD,EAAE0F,MAAQ1F,EAEV4F,IAAIlB,EAAQvF,MAAe0B,EAA3B1B,MAA+Ca,EAACjB,KAAU,GAC1DiB,EAAAb,IAAWuF,EACXA,EAAQ5F,IAAQ+B,EAAhB/B,IACA4F,EAAQ/F,IAAakC,EACrB6D,IAAAA,EAAA/F,IAAmBuH,QAAQ,SAAAxH,GACtBA,IAAOA,EAAAE,GAAgB8F,EAC3B,GAEQxH,EAAI,EAAGA,EAAI8C,EAAA2F,IAAkBvH,OAAQlB,IAC7C8C,EAACf,IAAkBgB,KAAKD,EAAA2F,IAAkBzI,IAE3C8C,EAAC2F,IAAmB,GAEhB3F,EAACf,IAAkBb,QACtBwC,EAAYX,KAAKD,GAGlB,MAAM4C,CACN,CAE4B,MAAzB5C,EAAEmG,qBACLnG,EAAEmG,oBAAoBjD,EAAUlD,MAAcgF,GAGnB,MAAxBhF,EAAEoG,oBACLpG,EAAAf,IAAmBgB,KAAK,WACvBD,EAAEoG,mBAAmBjD,EAAUyB,EAAUC,EACzC,EAEF,CASD,GAPA7E,EAAEV,QAAU0F,EACZhF,EAAExC,MAAQ0F,EACVlD,EAAAb,IAAWuF,EACX1E,EAACgB,IAAcD,EAEXkE,EAAapI,EAAjByD,IACC4E,EAAQ,EACL,cAAeC,GAAWA,EAAQG,UAAUC,OAAQ,CAQvD,IAPAvF,EAAE0F,MAAQ1F,EACVA,IAAAA,EAAAjB,KAAW,EAEPkG,GAAYA,EAAWP,GAE3BjC,EAAMzC,EAAEuF,OAAOvF,EAAExC,MAAOwC,EAAE0F,MAAO1F,EAAEV,SAE1BpC,EAAI,EAAGA,EAAI8C,EAAA2F,IAAkBvH,OAAQlB,IAC7C8C,EAACf,IAAkBgB,KAAKD,EAAA2F,IAAkBzI,IAE3C8C,EAAC2F,IAAmB,EACpB,MACA,GACC3F,OAAW,EACPiF,GAAYA,EAAWP,GAE3BjC,EAAMzC,EAAEuF,OAAOvF,EAAExC,MAAOwC,EAAE0F,MAAO1F,EAAEV,SAGnCU,EAAE0F,MAAQ1F,EACV4F,UAAQ5F,EAAAjB,OAAcmG,EAAQ,IAIhClF,EAAE0F,MAAQ1F,EAAV4F,IAEyB,MAArB5F,EAAEqG,kBACL7E,EAAgBlE,EAAOA,EAAO,CAAD,EAAKkE,GAAgBxB,EAAEqG,oBAGhD1B,GAAsC,MAA7B3E,EAAEsG,0BACfzB,EAAW7E,EAAEsG,wBAAwBnD,EAAUyB,IAK5CvD,EADI,MAAPoB,GAAeA,EAAI3E,OAASsB,GAAuB,MAAXqD,EAAIzE,IACLyE,EAAIjF,MAAMO,SAAW0E,EAE7DrB,EACCL,EACAoB,MAAMC,QAAQf,GAAgBA,EAAe,CAACA,GAC9CqD,EACA7D,EACAW,EACAC,EACAC,EACAd,EACAE,EACAa,GAGD3B,EAAEF,KAAO4E,EAGTA,IAAAA,EAAAzF,IAAsB,KAElBe,EAAAf,IAAmBb,QACtBwC,EAAYX,KAAKD,GAGd8E,IACH9E,EAACqF,IAAiBrF,EAAApB,GAAyB,MAG5CoB,EAAClB,KAAU,CACX,MACqB,MAArB4C,GACAgD,EAAAvF,MAAuB0B,EAFjB1B,KAINuF,EAAA/F,IAAqBkC,EAArBlC,IACA+F,EAAQ5F,IAAQ+B,EAChB/B,KACA4F,EAAQ5F,IAAQyH,EACf1F,EACA6D,IAAAA,EACA7D,EACAW,EACAC,EACAC,EACAd,EACAe,IAIGc,EAAM5F,EAAQ2J,SAAS/D,EAAIiC,EAYhC,CAXC,MAAOJ,GACRI,EAAAvF,IAAqB,MAEjBwC,GAAoC,MAArBD,KAClBgD,EAAA5F,IAAgBgC,EAChB4D,EAAQzF,MAAgB0C,EACxBD,EAAkBA,EAAkBhC,QAAQoB,IAAW,MAIxDjE,EAAAiC,IAAoBwF,EAAGI,EAAU7D,EACjC,CACD,CAOM,SAASM,EAAWP,EAAa6F,GACnC5J,EAAiBA,KAAAA,EAAAmC,IAAgByH,EAAM7F,GAE3CA,EAAYF,KAAK,SAAAV,GAChB,IAECY,EAAcZ,EAAdf,IACAe,EAACf,IAAoB,GACrB2B,EAAYF,KAAK,SAAAgG,GAEhBA,EAAGrI,KAAK2B,EACR,EAGD,CAFC,MAAOsE,GACRzH,EAAAiC,IAAoBwF,EAAGtE,EAAvBb,IACA,CACD,EACD,CAgBD,SAASoH,EACRtD,EACAyB,EACA7D,EACAW,EACAC,EACAC,EACAd,EACAe,GARD,IAoBS9B,EAsDH8G,EACAC,EAjEDzD,EAAWtC,EAASrD,MACpB0F,EAAWwB,EAASlH,MACpBqJ,EAAWnC,EAAS5G,KACpBZ,EAAI,EAKR,GAFiB,QAAb2J,IAAoBpF,GAAQ,GAEP,MAArBC,EACH,KAAOxE,EAAIwE,EAAkBtD,OAAQlB,IAMpC,IALM2C,EAAQ6B,EAAkBxE,KAO/B,iBAAkB2C,KAAYgH,IAC7BA,EAAWhH,EAAMiH,YAAcD,EAA8B,IAAnBhH,EAAMgH,UAChD,CACD5D,EAAMpD,EACN6B,EAAkBxE,GAAK,KACvB,KACA,CAIH,GAAW,MAAP+F,EAAa,CAChB,GAAiB,OAAb4D,EAEH,OAAOE,SAASC,eAAe9D,GAI/BD,EADGxB,EACGsF,SAASE,gBACd,6BAEAJ,GAGKE,SAASlJ,cAEdgJ,EACA3D,EAASgE,IAAMhE,GAKjBxB,EAAoB,KAEpBC,GAAc,CACd,CAED,GAAiB,OAAbkF,EAEC1D,IAAaD,GAAcvB,GAAesB,EAAIkE,OAASjE,IAC1DD,EAAIkE,KAAOjE,OAEN,CAWN,GATAxB,EAAoBA,GAAqB9E,EAAMyB,KAAK4E,EAAImE,YAIpDT,GAFJxD,EAAWtC,EAASrD,OAASL,GAENkK,wBACnBT,EAAU1D,EAASmE,yBAIlB1F,EAAa,CAGjB,GAAyB,MAArBD,EAEH,IADAyB,EAAW,CAAA,EACNjG,EAAI,EAAGA,EAAI+F,EAAIqE,WAAWlJ,OAAQlB,IACtCiG,EAASF,EAAIqE,WAAWpK,GAAGwG,MAAQT,EAAIqE,WAAWpK,GAAGsG,OAInDoD,GAAWD,KAGZC,IACED,GAAWC,EAAOW,QAAWZ,EAAlBY,QACbX,EAAAW,SAAmBtE,EAAIuE,aAExBvE,EAAIuE,UAAaZ,GAAWA,EAAZW,QAA+B,IAGjD,CAKD,GAHAvE,EAAUC,EAAKC,EAAUC,EAAU1B,EAAOE,GAGtCiF,EACHlC,EAAQ/F,IAAa,QAmBrB,GAjBAzB,EAAIwH,EAASlH,MAAMO,SACnBqD,EACC6B,EACAd,MAAMC,QAAQlF,GAAKA,EAAI,CAACA,GACxBwH,EACA7D,EACAW,EACAC,GAAsB,kBAAboF,EACTnF,EACAd,EACAc,EACGA,EAAkB,GAClBb,EAAAlC,KAAsBa,EAAcqB,EAAU,GACjDc,GAIwB,MAArBD,EACH,IAAKxE,EAAIwE,EAAkBtD,OAAQlB,KACN,MAAxBwE,EAAkBxE,IAAYO,EAAWiE,EAAkBxE,IAM7DyE,IAEH,UAAWuB,QACc3E,KAAxBrB,EAAIgG,EAASM,SAKbtG,IAAM+F,EAAIO,OACI,aAAbqD,IAA4B3J,GAIf,WAAb2J,GAAyB3J,IAAMiG,EAASK,QAE1CH,EAAYJ,EAAK,QAAS/F,EAAGiG,EAASK,OAAO,GAG7C,YAAaN,QACc3E,KAA1BrB,EAAIgG,EAASuE,UACdvK,IAAM+F,EAAIwE,SAEVpE,EAAYJ,EAAK,UAAW/F,EAAGiG,EAASsE,SAAS,GAGnD,CAED,OAAOxE,CACP,CAQeT,SAAAA,EAASvE,EAAKuF,EAAO9E,GACpC,IACmB,mBAAPT,EAAmBA,EAAIuF,GAC7BvF,EAAIyJ,QAAUlE,CAGnB,CAFC,MAAOc,GACRzH,EAAAiC,IAAoBwF,EAAG5F,EACvB,CACD,CAUM,SAAS6D,EAAQ7D,EAAOiJ,EAAaC,GAArC,IACFC,EAuBM3K,EAdV,GARIL,EAAQ0F,SAAS1F,EAAQ0F,QAAQ7D,IAEhCmJ,EAAInJ,EAAMT,OACT4J,EAAEH,SAAWG,EAAEH,UAAYhJ,EAAdI,KACjB0D,EAASqF,EAAG,KAAMF,IAIU,OAAzBE,EAAInJ,EAAHM,KAA8B,CACnC,GAAI6I,EAAEC,qBACL,IACCD,EAAEC,sBAGF,CAFC,MAAOxD,GACRzH,EAAOiC,IAAawF,EAAGqD,EACvB,CAGFE,EAAE/H,KAAO+H,EAAA7G,IAAe,KACxBtC,EAAKM,SAAcT,CACnB,CAED,GAAKsJ,EAAInJ,EAAHC,IACL,IAASzB,EAAI,EAAGA,EAAI2K,EAAEzJ,OAAQlB,IACzB2K,EAAE3K,IACLqF,EACCsF,EAAE3K,GACFyK,EACAC,GAAoC,mBAAflJ,EAAMZ,MAM1B8J,GAA4B,MAAdlJ,EAAKI,KACvBrB,EAAWiB,EAADI,KAKXJ,EAAAE,GAAgBF,EAAKI,IAAQJ,EAAAK,SAAiBR,CAC9C,CAGD,SAASiH,EAAShI,EAAOkI,EAAOpG,GAC/B,OAAYJ,KAAAA,YAAY1B,EAAO8B,EAC/B,CC5hBM,SAASiG,EAAO7G,EAAOqC,EAAWgH,GAAlC,IAMFpG,EAOAd,EAUAD,EAtBA/D,EAAeA,IAAAA,EAAA+B,GAAcF,EAAOqC,GAYpCF,GAPAc,EAAqC,mBAAhBoG,GAQtB,KACCA,GAAeA,OAA0BhH,MAQzCH,EAAc,GAClBK,EACCF,EARDrC,IACGiD,GAAeoG,GACjBhH,GAFOpC,IAGMd,EAAcuB,EAAU,KAAM,CAACV,IAS5CmC,GAAY1D,EACZA,OAC8BoB,IAA9BwC,EAAUG,iBACTS,GAAeoG,EACb,CAACA,GACDlH,EACA,KACAE,EAAUiH,WACVpL,EAAMyB,KAAK0C,EAAUqG,YACrB,KACHxG,GACCe,GAAeoG,EACbA,EACAlH,EACAA,EACAE,IAAAA,EAAUiH,WACbrG,GAIDR,EAAWP,EAAalC,EACxB,CTtCY9B,EAAQQ,EAAUR,MCfzBC,EAAU,CACfiC,ISHM,SAAqBmJ,EAAOvJ,EAAOmC,EAAUqH,GAInD,IAFA,IAAIvH,EAAWwH,EAAMC,EAEb1J,EAAQA,EAAhBE,IACC,IAAK+B,EAAYjC,EAAHM,OAAyB2B,EAAD/B,GACrC,IAcC,IAbAuJ,EAAOxH,EAAUzB,cAE4B,MAAjCiJ,EAAKE,2BAChB1H,EAAU2H,SAASH,EAAKE,yBAAyBJ,IACjDG,EAAUzH,EAAH5B,KAG2B,MAA/B4B,EAAU4H,oBACb5H,EAAU4H,kBAAkBN,EAAOC,GAAa,CAAhD,GACAE,EAAUzH,EACV5B,KAGGqJ,EACH,OAAQzH,EAAS0E,IAAiB1E,CAInC,CAFC,MAAO2D,GACR2D,EAAQ3D,CACR,CAIH,MAAM2D,CACN,GRpCGnL,EAAU,EA6FDC,EAAiB,SAAA2B,UACpB,MAATA,QAAuCH,IAAtBG,EAAMQ,WADW,ECtEnCG,EAAUiG,UAAUgD,SAAW,SAASE,EAAQC,GAE/C,IAAIC,EAEHA,EADsB,MAAnBnJ,KAAAqG,KAA2BrG,WAAoBA,KAAKmG,MACnDnG,KACJqG,IACIrG,KAAAqG,IAAkBtI,EAAO,CAAD,EAAKiC,KAAKmG,OAGlB,mBAAV8C,IAGVA,EAASA,EAAOlL,EAAO,GAAIoL,GAAInJ,KAAK/B,QAGjCgL,GACHlL,EAAOoL,EAAGF,GAIG,MAAVA,GAEAjJ,KAAaJ,MACZsJ,GACHlJ,KAAAoG,IAAqB1F,KAAKwI,GAE3B1I,EAAcR,MAEf,EAQDF,EAAUiG,UAAUqD,YAAc,SAASF,GACtClJ,WAIHA,KAAAT,KAAc,EACV2J,GAAUlJ,KAAsBU,IAAAA,KAAKwI,GACzC1I,EAAcR,MAEf,EAYDF,EAAUiG,UAAUC,OAASnG,EAyFzBpC,EAAgB,GA4CpBkD,EAAOI,IAAkB,ECtNdpD,EAAI,qCKsECkG,SAAAA,EAAQ1E,EAAOqC,GAC9BwE,EAAO7G,EAAOqC,EAAWqC,EACzB,2CPSM,WACN,MAAO,CAAEsE,QAAS,KAClB,qDS3E4BhJ,EAAOlB,EAAOO,GAC1C,IACCC,EACAC,EACAf,EAHGgB,EAAkBZ,EAAO,CAAA,EAAIoB,EAAMlB,OAIvC,IAAKN,KAAKM,EACA,OAALN,EAAYc,EAAMR,EAAMN,GACd,OAALA,EAAYe,EAAMT,EAAMN,GAC5BgB,EAAgBhB,GAAKM,EAAMN,GAQjC,OALIiB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIxB,EAAMyB,KAAKF,UAAW,GAAKJ,GAG7CS,EACNE,EAAMZ,KACNI,EACAF,GAAOU,EAAMV,IACbC,GAAOS,EAAMT,IACb,KAED,gBP7BM,SAAuB2K,EAAcC,GAG3C,IAAMvJ,EAAU,CACfN,IAHD6J,EAAY,OAAS3L,IAIpB0B,GAAegK,EAEfE,SAJe,SAINtL,EAAOuL,GAIf,OAAOvL,EAAMO,SAASgL,EACtB,EAEDC,kBAASxL,OAEHyL,EACAC,EAmCL,OArCK3J,KAAK8G,kBACL4C,EAAO,IACPC,EAAM,CAAV,GACIL,GAAatJ,KAEjBA,KAAK8G,gBAAkB,WAAA,OAAM6C,CAAN,EAEvB3J,KAAK0G,sBAAwB,SAASkD,GACjC5J,KAAK/B,MAAMgG,QAAU2F,EAAO3F,OAe/ByF,EAAKvI,KAAKX,EAEX,EAEDR,KAAKkG,IAAM,SAAAzF,GACViJ,EAAKhJ,KAAKD,GACV,IAAIoJ,EAAMpJ,EAAE8H,qBACZ9H,EAAE8H,qBAAuB,WACxBmB,EAAKI,OAAOJ,EAAKvJ,QAAQM,GAAI,GACzBoJ,GAAKA,EAAI/K,KAAK2B,EAClB,CACD,GAGKxC,EAAMO,QACb,GASF,OAAQuB,EAAQ0J,SAAuB1J,GAAAA,EAAQwJ,SAAS1D,YAAc9F,CACtE,eEsLegK,SAAAA,EAAavL,EAAUwL,GAUtC,OATAA,EAAMA,GAAO,GACG,MAAZxL,GAAuC,kBAAZA,IACpBoE,MAAMC,QAAQrE,GACxBA,EAAS2C,KAAK,SAAAb,GACbyJ,EAAazJ,EAAO0J,EACpB,GAEDA,EAAItJ,KAAKlC,IAEHwL,CACP,oBMnQUC,OAAS,IAAKA,OAAOC,QAAUC,EACrCC,KAAKD,OAASA"}