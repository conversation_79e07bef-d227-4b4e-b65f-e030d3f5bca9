{"version": 3, "file": "jsx.js", "sources": ["jsx.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?e(exports,require(\"preact\")):\"function\"==typeof define&&define.amd?define([\"exports\",\"preact\"],e):e((t||self).preactRenderToString={},t.preact)}(this,function(t,e){if(\"function\"!=typeof Symbol){var n=0;Symbol=function(t){return\"@@\"+t+ ++n},Symbol.for=function(t){return\"@@\"+t}}var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,o=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,i=/[\\s\\n\\\\/='\"\\0<>]/,a=/^xlink:?./,s=/[\"&<]/;function l(t){if(!1===s.test(t+=\"\"))return t;for(var e=0,n=0,r=\"\",o=\"\";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o=\"&quot;\";break;case 38:o=\"&amp;\";break;case 60:o=\"&lt;\";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var c=function(t,e){return String(t).replace(/(\\n+)/g,\"$1\"+(e||\"\\t\"))},f=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf(\"\\n\")||-1!==String(t).indexOf(\"<\")},u={},p=/([A-Z])/g;function d(t){var e=\"\";for(var n in t){var o=t[n];null!=o&&\"\"!==o&&(e&&(e+=\" \"),e+=\"-\"==n[0]?n:u[n]||(u[n]=n.replace(p,\"-$1\").toLowerCase()),e=\"number\"==typeof o&&!1===r.test(n)?e+\": \"+o+\"px;\":e+\": \"+o+\";\")}return e||void 0}function y(t,e){return Array.isArray(e)?e.reduce(y,t):null!=e&&!1!==e&&t.push(e),t}function g(){this.__d=!0}function _(t,e){return{__v:t,context:e,props:t.props,setState:g,forceUpdate:g,__d:!0,__h:[]}}function v(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var b=[];function m(t,n,r,s,u,p){if(null==t||\"boolean\"==typeof t)return\"\";if(\"object\"!=typeof t)return l(t);var g=r.pretty,h=g&&\"string\"==typeof g?g:\"\\t\";if(Array.isArray(t)){for(var j=\"\",x=0;x<t.length;x++)g&&x>0&&(j+=\"\\n\"),j+=m(t[x],n,r,s,u,p);return j}var S,k=t.type,A=t.props,O=!1;if(\"function\"==typeof k){if(O=!0,!r.shallow||!s&&!1!==r.renderRootComponent){if(k===e.Fragment){var w=[];return y(w,t.props.children),m(w,n,r,!1!==r.shallowHighOrder,u,p)}var F,C=t.__c=_(t,n);e.options.__b&&e.options.__b(t);var E=e.options.__r;if(k.prototype&&\"function\"==typeof k.prototype.render){var M=v(k,n);(C=t.__c=new k(A,M)).__v=t,C._dirty=C.__d=!0,C.props=A,null==C.state&&(C.state={}),null==C._nextState&&null==C.__s&&(C._nextState=C.__s=C.state),C.context=M,k.getDerivedStateFromProps?C.state=Object.assign({},C.state,k.getDerivedStateFromProps(C.props,C.state)):C.componentWillMount&&(C.componentWillMount(),C.state=C._nextState!==C.state?C._nextState:C.__s!==C.state?C.__s:C.state),E&&E(t),F=C.render(C.props,C.state,C.context)}else for(var H=v(k,n),N=0;C.__d&&N++<25;)C.__d=!1,E&&E(t),F=k.call(t.__c,A,H);return C.getChildContext&&(n=Object.assign({},n,C.getChildContext())),e.options.diffed&&e.options.diffed(t),m(F,n,r,!1!==r.shallowHighOrder,u,p)}k=(S=k).displayName||S!==Function&&S.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\\s*function\\s+([^( ]+)/)||\"\")[1];if(!e){for(var n=-1,r=b.length;r--;)if(b[r]===t){n=r;break}n<0&&(n=b.push(t)-1),e=\"UnnamedComponent\"+n}return e}(S)}var D,I,T=\"<\"+k;if(A){var L=Object.keys(A);r&&!0===r.sortAttributes&&L.sort();for(var W=0;W<L.length;W++){var $=L[W],P=A[$];if(\"children\"!==$){if(!i.test($)&&(r&&r.allAttributes||\"key\"!==$&&\"ref\"!==$&&\"__self\"!==$&&\"__source\"!==$)){if(\"defaultValue\"===$)$=\"value\";else if(\"defaultChecked\"===$)$=\"checked\";else if(\"defaultSelected\"===$)$=\"selected\";else if(\"className\"===$){if(void 0!==A.class)continue;$=\"class\"}else u&&a.test($)&&($=$.toLowerCase().replace(/^xlink:?/,\"xlink:\"));if(\"htmlFor\"===$){if(A.for)continue;$=\"for\"}\"style\"===$&&P&&\"object\"==typeof P&&(P=d(P)),\"a\"===$[0]&&\"r\"===$[1]&&\"boolean\"==typeof P&&(P=String(P));var U=r.attributeHook&&r.attributeHook($,P,n,r,O);if(U||\"\"===U)T+=U;else if(\"dangerouslySetInnerHTML\"===$)I=P&&P.__html;else if(\"textarea\"===k&&\"value\"===$)D=P;else if((P||0===P||\"\"===P)&&\"function\"!=typeof P){if(!(!0!==P&&\"\"!==P||(P=$,r&&r.xml))){T=T+\" \"+$;continue}if(\"value\"===$){if(\"select\"===k){p=P;continue}\"option\"===k&&p==P&&void 0===A.selected&&(T+=\" selected\")}T=T+\" \"+$+'=\"'+l(P)+'\"'}}}else D=P}}if(g){var R=T.replace(/\\n\\s*/,\" \");R===T||~R.indexOf(\"\\n\")?g&&~T.indexOf(\"\\n\")&&(T+=\"\\n\"):T=R}if(T+=\">\",i.test(k))throw new Error(k+\" is not a valid HTML tag name in \"+T);var J,V=o.test(k)||r.voidElements&&r.voidElements.test(k),q=[];if(I)g&&f(I)&&(I=\"\\n\"+h+c(I,h)),T+=I;else if(null!=D&&y(J=[],D).length){for(var z=g&&~T.indexOf(\"\\n\"),B=!1,G=0;G<J.length;G++){var Z=J[G];if(null!=Z&&!1!==Z){var K=m(Z,n,r,!0,\"svg\"===k||\"foreignObject\"!==k&&u,p);if(g&&!z&&f(K)&&(z=!0),K)if(g){var Q=K.length>0&&\"<\"!=K[0];B&&Q?q[q.length-1]+=K:q.push(K),B=Q}else q.push(K)}}if(g&&z)for(var X=q.length;X--;)q[X]=\"\\n\"+h+c(q[X],h)}if(q.length||I)T+=q.join(\"\");else if(r&&r.xml)return T.substring(0,T.length-1)+\" />\";return!V||J||I?(g&&~T.indexOf(\"\\n\")&&(T+=\"\\n\"),T=T+\"</\"+k+\">\"):T=T.replace(/>$/,\" />\"),T}var h={shallow:!0};x.render=x;var j=[];function x(t,n,r){n=n||{};var o,i=e.options.__s;return e.options.__s=!0,o=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):w(t,n,!1,void 0),e.options.__c&&e.options.__c(t,j),e.options.__s=i,j.length=0,o}function S(t,e){return\"className\"===t?\"class\":\"htmlFor\"===t?\"for\":\"defaultValue\"===t?\"value\":\"defaultChecked\"===t?\"checked\":\"defaultSelected\"===t?\"selected\":e&&a.test(t)?t.toLowerCase().replace(/^xlink:?/,\"xlink:\"):t}function k(t,e){return\"style\"===t&&null!=e&&\"object\"==typeof e?d(e):\"a\"===t[0]&&\"r\"===t[1]&&\"boolean\"==typeof e?String(e):e}var A=Array.isArray,O=Object.assign;function w(t,n,r,a){if(null==t||!0===t||!1===t||\"\"===t)return\"\";if(\"object\"!=typeof t)return l(t);if(A(t)){for(var s=\"\",c=0;c<t.length;c++)s+=w(t[c],n,r,a);return s}e.options.__b&&e.options.__b(t);var f=t.type,u=t.props;if(\"function\"==typeof f){if(f===e.Fragment)return w(t.props.children,n,r,a);var p;p=f.prototype&&\"function\"==typeof f.prototype.render?function(t,n){var r=t.type,o=v(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=O({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.options.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(t,n):function(t,n){var r,o=_(t,n),i=v(t.type,n);t.__c=o;for(var a=e.options.__r,s=0;o.__d&&s++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(t,n);var d=t.__c;d.getChildContext&&(n=O({},n,d.getChildContext()));var y=w(p,n,r,a);return e.options.diffed&&e.options.diffed(t),y}var g,b,m=\"<\";if(m+=f,u)for(var h in g=u.children,u){var j=u[h];if(!(\"key\"===h||\"ref\"===h||\"__self\"===h||\"__source\"===h||\"children\"===h||\"className\"===h&&\"class\"in u||\"htmlFor\"===h&&\"for\"in u||i.test(h)))if(j=k(h=S(h,r),j),\"dangerouslySetInnerHTML\"===h)b=j&&j.__html;else if(\"textarea\"===f&&\"value\"===h)g=j;else if((j||0===j||\"\"===j)&&\"function\"!=typeof j){if(!0===j||\"\"===j){j=h,m=m+\" \"+h;continue}if(\"value\"===h){if(\"select\"===f){a=j;continue}\"option\"!==f||a!=j||\"selected\"in u||(m+=\" selected\")}m=m+\" \"+h+'=\"'+l(j)+'\"'}}var x=m;if(m+=\">\",i.test(f))throw new Error(f+\" is not a valid HTML tag name in \"+m);var F=\"\",C=!1;if(b)F+=b,C=!0;else if(\"string\"==typeof g)F+=l(g),C=!0;else if(A(g))for(var E=0;E<g.length;E++){var M=g[E];if(null!=M&&!1!==M){var H=w(M,n,\"svg\"===f||\"foreignObject\"!==f&&r,a);H&&(F+=H,C=!0)}}else if(null!=g&&!1!==g&&!0!==g){var N=w(g,n,\"svg\"===f||\"foreignObject\"!==f&&r,a);N&&(F+=N,C=!0)}if(e.options.diffed&&e.options.diffed(t),C)m+=F;else if(o.test(f))return x+\" />\";return m+\"</\"+f+\">\"}x.shallowRender=function(t,e){return x(t,e,h)};const F=/(\\\\|\\\"|\\')/g,C=Object.prototype.toString,E=Date.prototype.toISOString,M=Error.prototype.toString,H=RegExp.prototype.toString,N=Symbol.prototype.toString,D=/^Symbol\\((.*)\\)(.*)$/,I=/\\n/gi,T=Object.getOwnPropertySymbols||(t=>[]);function L(t){return\"[object Array]\"===t||\"[object ArrayBuffer]\"===t||\"[object DataView]\"===t||\"[object Float32Array]\"===t||\"[object Float64Array]\"===t||\"[object Int8Array]\"===t||\"[object Int16Array]\"===t||\"[object Int32Array]\"===t||\"[object Uint8Array]\"===t||\"[object Uint8ClampedArray]\"===t||\"[object Uint16Array]\"===t||\"[object Uint32Array]\"===t}function W(t){return\"\"===t.name?\"[Function anonymous]\":\"[Function \"+t.name+\"]\"}function $(t){return N.call(t).replace(D,\"Symbol($1)\")}function P(t){return\"[\"+M.call(t)+\"]\"}function U(t){if(!0===t||!1===t)return\"\"+t;if(void 0===t)return\"undefined\";if(null===t)return\"null\";const e=typeof t;if(\"number\"===e)return function(t){return t!=+t?\"NaN\":0===t&&1/t<0?\"-0\":\"\"+t}(t);if(\"string\"===e)return'\"'+function(t){return t.replace(F,\"\\\\$1\")}(t)+'\"';if(\"function\"===e)return W(t);if(\"symbol\"===e)return $(t);const n=C.call(t);return\"[object WeakMap]\"===n?\"WeakMap {}\":\"[object WeakSet]\"===n?\"WeakSet {}\":\"[object Function]\"===n||\"[object GeneratorFunction]\"===n?W(t,min):\"[object Symbol]\"===n?$(t):\"[object Date]\"===n?E.call(t):\"[object Error]\"===n?P(t):\"[object RegExp]\"===n?H.call(t):\"[object Arguments]\"===n&&0===t.length?\"Arguments []\":L(n)&&0===t.length?t.constructor.name+\" []\":t instanceof Error&&P(t)}function R(t,e,n,r,o,i,a,s,l,c){let f=\"\";if(t.length){f+=o;const u=n+e;for(let n=0;n<t.length;n++)f+=u+q(t[n],e,u,r,o,i,a,s,l,c),n<t.length-1&&(f+=\",\"+r);f+=o+n}return\"[\"+f+\"]\"}function J(t,e,n,r,o,i,a,s,l,c){if((i=i.slice()).indexOf(t)>-1)return\"[Circular]\";i.push(t);const f=++s>a;if(!f&&t.toJSON&&\"function\"==typeof t.toJSON)return q(t.toJSON(),e,n,r,o,i,a,s,l,c);const u=C.call(t);return\"[object Arguments]\"===u?f?\"[Arguments]\":function(t,e,n,r,o,i,a,s,l,c){return(c?\"\":\"Arguments \")+R(t,e,n,r,o,i,a,s,l,c)}(t,e,n,r,o,i,a,s,l,c):L(u)?f?\"[Array]\":function(t,e,n,r,o,i,a,s,l,c){return(c?\"\":t.constructor.name+\" \")+R(t,e,n,r,o,i,a,s,l,c)}(t,e,n,r,o,i,a,s,l,c):\"[object Map]\"===u?f?\"[Map]\":function(t,e,n,r,o,i,a,s,l,c){let f=\"Map {\";const u=t.entries();let p=u.next();if(!p.done){f+=o;const t=n+e;for(;!p.done;)f+=t+q(p.value[0],e,t,r,o,i,a,s,l,c)+\" => \"+q(p.value[1],e,t,r,o,i,a,s,l,c),p=u.next(),p.done||(f+=\",\"+r);f+=o+n}return f+\"}\"}(t,e,n,r,o,i,a,s,l,c):\"[object Set]\"===u?f?\"[Set]\":function(t,e,n,r,o,i,a,s,l,c){let f=\"Set {\";const u=t.entries();let p=u.next();if(!p.done){f+=o;const t=n+e;for(;!p.done;)f+=t+q(p.value[1],e,t,r,o,i,a,s,l,c),p=u.next(),p.done||(f+=\",\"+r);f+=o+n}return f+\"}\"}(t,e,n,r,o,i,a,s,l,c):\"object\"==typeof t?f?\"[Object]\":function(t,e,n,r,o,i,a,s,l,c){let f=(c?\"\":t.constructor?t.constructor.name+\" \":\"Object \")+\"{\",u=Object.keys(t).sort();const p=T(t);if(p.length&&(u=u.filter(t=>!(\"symbol\"==typeof t||\"[object Symbol]\"===C.call(t))).concat(p)),u.length){f+=o;const p=n+e;for(let n=0;n<u.length;n++){const d=u[n];f+=p+q(d,e,p,r,o,i,a,s,l,c)+\": \"+q(t[d],e,p,r,o,i,a,s,l,c),n<u.length-1&&(f+=\",\"+r)}f+=o+n}return f+\"}\"}(t,e,n,r,o,i,a,s,l,c):void 0}function V(t,e,n,r,o,i,a,s,l,c){let f,u=!1;for(let e=0;e<l.length;e++)if(f=l[e],f.test(t)){u=!0;break}return!!u&&f.print(t,function(t){return q(t,e,n,r,o,i,a,s,l,c)},function(t){const r=n+e;return r+t.replace(I,\"\\n\"+r)},{edgeSpacing:o,spacing:r})}function q(t,e,n,r,o,i,a,s,l,c){return U(t)||V(t,e,n,r,o,i,a,s,l,c)||J(t,e,n,r,o,i,a,s,l,c)}const z={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function B(t){return new Array(t+1).join(\" \")}var G={test:function(t){return t&&\"object\"==typeof t&&\"type\"in t&&\"props\"in t&&\"key\"in t},print:function(t,e,n){return x(t,G.context,G.opts)}},Z={plugins:[G]},K={attributeHook:function(t,e,n,r,o){var i=typeof e;if(\"dangerouslySetInnerHTML\"===t)return!1;if(null==e||\"function\"===i&&!r.functions)return\"\";if(r.skipFalseAttributes&&!o&&(!1===e||(\"class\"===t||\"style\"===t)&&\"\"===e))return\"\";var a=\"string\"==typeof r.pretty?r.pretty:\"\\t\";return\"string\"!==i?(\"function\"!==i||r.functionNames?(G.context=n,G.opts=r,~(e=function(t,e){let n,r;e?(function(t){if(Object.keys(t).forEach(t=>{if(!z.hasOwnProperty(t))throw new Error(\"prettyFormat: Invalid option: \"+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error(\"prettyFormat: Cannot run with min option and indent\")}(e),e=function(t){const e={};return Object.keys(z).forEach(n=>e[n]=t.hasOwnProperty(n)?t[n]:z[n]),e.min&&(e.indent=0),e}(e)):e=z;const o=e.min?\" \":\"\\n\",i=e.min?\"\":\"\\n\";if(e&&e.plugins.length){n=B(e.indent),r=[];var a=V(t,n,\"\",o,i,r,e.maxDepth,0,e.plugins,e.min);if(a)return a}return U(t)||(n||(n=B(e.indent)),r||(r=[]),J(t,n,\"\",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,Z)).indexOf(\"\\n\")&&(e=c(\"\\n\"+e,a)+\"\\n\")):e=\"Function\",c(\"\\n\"+t+\"={\"+e+\"}\",a)):\"\\n\"+a+t+'=\"'+l(e)+'\"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:\"  \"};function Q(t,e,n,r){return x(t,e,n=Object.assign({},K,n||{}))}t.default=Q,t.render=Q});\n//# sourceMappingURL=jsx.js.map\n"], "names": ["t", "Symbol", "n", "s", "c"], "mappings": "mEAAA,SAAsBA,GAAA,MAAXC,KAAAA,KAAuBC,GACjCD,WAEAA,SAASD,GAAUG,MAClB,KAAAH,OAAkBI,EAAAA"}