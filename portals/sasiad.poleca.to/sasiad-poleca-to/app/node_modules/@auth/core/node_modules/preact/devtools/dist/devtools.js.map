{"version": 3, "file": "devtools.js", "sources": ["../src/devtools.js", "../src/index.js"], "sourcesContent": ["import { options, Fragment, Component } from 'preact';\n\nexport function initDevTools() {\n\tif (typeof window != 'undefined' && window.__PREACT_DEVTOOLS__) {\n\t\twindow.__PREACT_DEVTOOLS__.attachPreact('10.11.3', options, {\n\t\t\tFragment,\n\t\t\tComponent\n\t\t});\n\t}\n}\n", "import { options } from 'preact';\nimport { initDevTools } from './devtools';\n\ninitDevTools();\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, name: string) => T}\n */\nexport function addHookName(value, name) {\n\tif (options._addHookName) {\n\t\toptions._addHookName(name);\n\t}\n\treturn value;\n}\n"], "names": ["window", "__PREACT_DEVTOOLS__", "attachPreact", "options", "Fragment", "Component", "value", "name", "__a"], "mappings": "wBAGsB,oBAAVA,QAAyBA,OAAOC,qBAC1CD,OAAOC,oBAAoBC,aAAa,UAAWC,EAAAA,QAAS,CAC3DC,SAAAA,EAAAA,SACAC,UAAAA,EAF2DA,gCCKvD,SAAqBC,EAAOC,GAIlC,OAHIJ,EAAAA,QAAsBK,KACzBL,EAAAA,QAAOK,IAAcD,GAEfD,CACP"}