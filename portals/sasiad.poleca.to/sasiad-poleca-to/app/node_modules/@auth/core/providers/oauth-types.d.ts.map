{"version": 3, "file": "oauth-types.d.ts", "sourceRoot": "", "sources": ["../src/providers/oauth-types.ts"], "names": [], "mappings": "AAEA,MAAM,MAAM,iBAAiB,GACzB,WAAW,GACX,OAAO,GACP,UAAU,GACV,OAAO,GACP,WAAW,GACX,cAAc,GACd,UAAU,GACV,cAAc,GACd,WAAW,GACX,WAAW,GACX,gBAAgB,GAChB,KAAK,GACL,aAAa,GACb,QAAQ,GACR,UAAU,GACV,SAAS,GACT,UAAU,GACV,SAAS,GACT,SAAS,GACT,UAAU,GACV,SAAS,GACT,yBAAyB,GACzB,WAAW,GACX,UAAU,GACV,QAAQ,GACR,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,kBAAkB,GAClB,WAAW,GACX,OAAO,GACP,UAAU,GACV,MAAM,GACN,UAAU,GACV,WAAW,GACX,QAAQ,GACR,UAAU,GACV,YAAY,GACZ,QAAQ,GACR,oBAAoB,GACpB,OAAO,GACP,SAAS,GACT,UAAU,GACV,YAAY,GACZ,QAAQ,GACR,MAAM,GACN,UAAU,GACV,WAAW,GACX,MAAM,GACN,KAAK,GACL,SAAS,GACT,SAAS,GACT,SAAS,GACT,WAAW,GACX,WAAW,GACX,UAAU,GACV,QAAQ,GACR,QAAQ,GACR,YAAY,GACZ,UAAU,GACV,aAAa,GACb,OAAO,GACP,SAAS,GACT,QAAQ,GACR,SAAS,GACT,QAAQ,GACR,SAAS,GACT,OAAO,GACP,QAAQ,GACR,SAAS,GACT,gBAAgB,GAChB,IAAI,GACJ,UAAU,GACV,OAAO,GACP,WAAW,GACX,WAAW,GACX,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,MAAM,GACN,MAAM,CAAA"}