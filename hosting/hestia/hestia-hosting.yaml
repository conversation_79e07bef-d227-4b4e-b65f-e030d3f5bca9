# Configuration for hosting.gerasik.pl -> Hestia CP
http:
  middlewares:
    redirect-to-https:
      redirectscheme:
        scheme: https
  routers:
    hestia-http:
      middlewares:
        - redirect-to-https
      entryPoints:
        - http
      service: hestia
      rule: Host(`hosting.gerasik.pl`)
    hestia-https:
      entryPoints:
        - https
      service: hestia
      rule: Host(`hosting.gerasik.pl`)
      tls:
        certresolver: letsencrypt
  services:
    hestia:
      loadBalancer:
        servers:
          - url: 'https://********:8083'
        serversTransport: hestia-transport
  serversTransports:
    hestia-transport:
      insecureSkipVerify: true
