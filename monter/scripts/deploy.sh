#!/bin/bash

# Skrypt deploymentu aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Funkcje pomocnicze
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Zmienne konfiguracyjne
APP_NAME="klimatyzacja-sms-manager"
APP_DIR="/home/<USER>/$APP_NAME"
BACKUP_DIR="/home/<USER>/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Sprawdzenie czy katalog aplikacji istnieje
if [ ! -d "$APP_DIR" ]; then
    log_error "Katalog aplikacji $APP_DIR nie istnieje!"
    log_error "Uruchom najpierw skrypt setup-vps.sh"
    exit 1
fi

cd "$APP_DIR"

log_info "Rozpoczynam deployment aplikacji $APP_NAME"

# Krok 1: Backup obecnej wersji
log_step "1. Tworzenie backupu obecnej wersji..."
mkdir -p "$BACKUP_DIR"
if [ -d "dist" ]; then
    tar -czf "$BACKUP_DIR/dist_backup_$TIMESTAMP.tar.gz" dist/
    log_info "Backup utworzony: $BACKUP_DIR/dist_backup_$TIMESTAMP.tar.gz"
fi

# Krok 2: Zatrzymanie aplikacji
log_step "2. Zatrzymywanie aplikacji..."
pm2 stop $APP_NAME || log_warn "Aplikacja nie była uruchomiona"

# Krok 3: Aktualizacja kodu
log_step "3. Aktualizacja kodu z repozytorium..."
git fetch origin
git reset --hard origin/main
log_info "Kod zaktualizowany do najnowszej wersji"

# Krok 4: Instalacja/aktualizacja zależności
log_step "4. Instalacja zależności..."
npm ci --production=false

# Krok 5: Budowanie aplikacji
log_step "5. Budowanie aplikacji..."
npm run build

# Krok 6: Sprawdzenie czy build się powiódł
if [ ! -d "dist" ]; then
    log_error "Build nie powiódł się - brak katalogu dist!"
    log_info "Przywracanie backupu..."
    if [ -f "$BACKUP_DIR/dist_backup_$TIMESTAMP.tar.gz" ]; then
        tar -xzf "$BACKUP_DIR/dist_backup_$TIMESTAMP.tar.gz"
        log_info "Backup przywrócony"
    fi
    exit 1
fi

# Krok 7: Uruchomienie aplikacji
log_step "7. Uruchamianie aplikacji..."
pm2 start ecosystem.config.js || pm2 restart $APP_NAME

# Krok 8: Sprawdzenie statusu
log_step "8. Sprawdzanie statusu aplikacji..."
sleep 5
pm2 status

# Krok 9: Test health check
log_step "9. Test health check..."
if curl -f -s http://localhost:3000 > /dev/null; then
    log_info "✅ Aplikacja działa poprawnie!"
else
    log_error "❌ Aplikacja nie odpowiada!"
    log_info "Sprawdź logi: pm2 logs $APP_NAME"
    exit 1
fi

# Krok 10: Czyszczenie starych backupów (zachowaj ostatnie 5)
log_step "10. Czyszczenie starych backupów..."
cd "$BACKUP_DIR"
ls -t dist_backup_*.tar.gz 2>/dev/null | tail -n +6 | xargs -r rm
log_info "Stare backupy usunięte"

log_info "🎉 Deployment zakończony pomyślnie!"
log_info "Aplikacja dostępna pod adresem: http://$(hostname -I | awk '{print $1}')"
log_info "Status aplikacji: pm2 status"
log_info "Logi aplikacji: pm2 logs $APP_NAME"
