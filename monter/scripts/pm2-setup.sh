#!/bin/bash

# Skrypt konfiguracji PM2 dla aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Zmienne konfiguracyjne
APP_NAME="klimatyzacja-sms-manager"
APP_DIR="/home/<USER>/$APP_NAME"

log_info "Konfiguracja PM2 dla aplikacji $APP_NAME"

# Sprawdź czy PM2 jest zainstalowane
if ! command -v pm2 &> /dev/null; then
    log_error "PM2 nie jest zainstalowane!"
    log_info "Zainstaluj PM2: npm install -g pm2"
    exit 1
fi

# Przejdź do katalogu aplikacji
cd "$APP_DIR"

# Zainstaluj serve globalnie (do serwowania plików statycznych)
log_info "Instalacja serve..."
npm install -g serve

# Zatrzymaj wszystkie procesy PM2 dla tej aplikacji
log_info "Zatrzymywanie istniejących procesów..."
pm2 stop $APP_NAME 2>/dev/null || true
pm2 delete $APP_NAME 2>/dev/null || true

# Uruchom aplikację z pliku ecosystem.config.js
log_info "Uruchamianie aplikacji..."
pm2 start ecosystem.config.js --env production

# Zapisz konfigurację PM2
log_info "Zapisywanie konfiguracji PM2..."
pm2 save

# Konfiguracja startup (automatyczne uruchamianie po reboot)
log_info "Konfiguracja startup..."
STARTUP_CMD=$(pm2 startup | tail -n 1)
if [[ $STARTUP_CMD == sudo* ]]; then
    log_warn "Uruchom następujące polecenie jako sudo:"
    echo "$STARTUP_CMD"
    log_warn "Następnie uruchom ponownie ten skrypt"
else
    log_info "Startup już skonfigurowany"
fi

# Sprawdź status
log_info "Status aplikacji:"
pm2 status

# Konfiguracja monitoringu
log_info "Konfiguracja monitoringu..."
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true

# Opcjonalnie: Konfiguracja PM2 Plus (monitoring w chmurze)
read -p "Czy chcesz skonfigurować PM2 Plus monitoring? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Odwiedź https://app.pm2.io aby utworzyć konto"
    log_info "Następnie uruchom: pm2 link <secret_key> <public_key>"
fi

log_info "Konfiguracja PM2 zakończona!"
log_info "Przydatne komendy:"
log_info "  pm2 status                 - status aplikacji"
log_info "  pm2 logs $APP_NAME         - logi aplikacji"
log_info "  pm2 restart $APP_NAME      - restart aplikacji"
log_info "  pm2 stop $APP_NAME         - zatrzymanie aplikacji"
log_info "  pm2 monit                  - monitoring w czasie rzeczywistym"
