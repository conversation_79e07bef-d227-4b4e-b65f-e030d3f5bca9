// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wppdalfaszlofizgehqn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndwcGRhbGZhc3psb2ZpemdlaHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjA2NjgsImV4cCI6MjA2NTU5NjY2OH0.-aCM0e5UOqyx_iLUYkvT4qtffPeinGRww-GxkdStCQY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);