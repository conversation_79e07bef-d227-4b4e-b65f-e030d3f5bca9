
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

const fetchDashboardStats = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  // Fetch customers count
  const { count: customersCount } = await supabase
    .from('customers')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id);

  // Fetch customers with upcoming maintenance
  const { count: upcomingMaintenanceCount } = await supabase
    .from('customers')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .not('next_maintenance', 'is', null);

  // Fetch SMS history count for today
  const today = new Date().toISOString().split('T')[0];
  const { count: todaysSmsCount } = await supabase
    .from('sms_history')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .gte('sent_at', today);

  // Fetch user profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  return {
    customersCount: customersCount || 0,
    upcomingMaintenanceCount: upcomingMaintenanceCount || 0,
    todaysSmsCount: todaysSmsCount || 0,
    profile
  };
};

export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: fetchDashboardStats
  });
};
