import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

export interface SmsApiConfig {
  token: string;
  from?: string;
}

export interface SmsMessage {
  to: string;
  message: string;
  from?: string;
}

export interface SmsResponse {
  count: number;
  list: Array<{
    id: string;
    points: number;
    number: string;
    date_sent: string;
    submitted_number: string;
    status: string;
    error?: string;
  }>;
}

export interface SmsError {
  error: string;
  message: string;
  code?: number;
}

const SMS_LIMIT = 160;
const SMSAPI_BASE_URL = 'https://api.smsapi.pl';

// Calculate SMS count based on message length
export const calculateSmsCount = (message: string): number => {
  return Math.ceil(message.length / SMS_LIMIT);
};

// Validate SMS message
export const validateSmsMessage = (message: string): { isValid: boolean; error?: string } => {
  if (!message || message.trim().length === 0) {
    return { isValid: false, error: 'Wiadomość nie może być pusta' };
  }
  
  if (message.length > SMS_LIMIT * 10) { // Max 10 SMS parts
    return { isValid: false, error: 'Wiadomość jest zbyt długa (maksymalnie 1600 znaków)' };
  }
  
  return { isValid: true };
};

// Replace variables in message template
export const replaceMessageVariables = (
  template: string,
  variables: {
    data?: string;
    godzina?: string;
    adres?: string;
    imie?: string;
    nazwisko?: string;
    telefon?: string;
  }
): string => {
  let message = template;
  
  if (variables.data) {
    message = message.replace(/{data}/g, variables.data);
  }
  if (variables.godzina) {
    message = message.replace(/{godzina}/g, variables.godzina);
  }
  if (variables.adres) {
    message = message.replace(/{adres}/g, variables.adres);
  }
  if (variables.imie) {
    message = message.replace(/{imie}/g, variables.imie);
  }
  if (variables.nazwisko) {
    message = message.replace(/{nazwisko}/g, variables.nazwisko);
  }
  if (variables.telefon) {
    message = message.replace(/{telefon}/g, variables.telefon);
  }
  
  return message;
};

// Send SMS via SMSAPI
const sendSms = async (config: SmsApiConfig, smsData: SmsMessage): Promise<SmsResponse> => {
  const validation = validateSmsMessage(smsData.message);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  const formData = new FormData();
  formData.append('to', smsData.to);
  formData.append('message', smsData.message);
  if (smsData.from) {
    formData.append('from', smsData.from);
  }
  formData.append('format', 'json');

  const response = await fetch(`${SMSAPI_BASE_URL}/sms.do`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${config.token}`,
    },
    body: formData,
  });

  const data = await response.json();

  if (!response.ok) {
    const error = data as SmsError;
    let errorMessage = 'Błąd wysyłania SMS';
    
    switch (error.code) {
      case 101:
        errorMessage = 'Nieprawidłowy token autoryzacji';
        break;
      case 102:
        errorMessage = 'Nieprawidłowy numer telefonu';
        break;
      case 103:
        errorMessage = 'Brak wystarczających środków na koncie';
        break;
      case 104:
        errorMessage = 'Brak dostępu do usługi SMS';
        break;
      case 105:
        errorMessage = 'Wiadomość jest zbyt długa';
        break;
      case 106:
        errorMessage = 'Nieprawidłowa nazwa nadawcy';
        break;
      case 107:
        errorMessage = 'Przekroczono limit wysyłek';
        break;
      default:
        errorMessage = error.message || error.error || 'Nieznany błąd SMSAPI';
    }
    
    throw new Error(errorMessage);
  }

  return data as SmsResponse;
};

// Check account balance
const checkBalance = async (config: SmsApiConfig): Promise<{ points: number }> => {
  const response = await fetch(`${SMSAPI_BASE_URL}/profile.do?format=json`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${config.token}`,
    },
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error('Nie udało się sprawdzić salda konta');
  }

  return { points: data.points || 0 };
};

// Test SMS API connection
const testConnection = async (config: SmsApiConfig): Promise<boolean> => {
  try {
    await checkBalance(config);
    return true;
  } catch (error) {
    return false;
  }
};

export const useSmsApi = (config?: SmsApiConfig) => {
  const { toast } = useToast();

  const { data: balance, isLoading: balanceLoading } = useQuery({
    queryKey: ['sms-balance', config?.token],
    queryFn: () => config ? checkBalance(config) : Promise.resolve({ points: 0 }),
    enabled: !!config?.token,
    refetchInterval: 300000, // Refresh every 5 minutes
  });

  const sendSmsMutation = useMutation({
    mutationFn: ({ smsData }: { smsData: SmsMessage }) => {
      if (!config) throw new Error('Konfiguracja SMSAPI nie została ustawiona');
      return sendSms(config, smsData);
    },
    onSuccess: (data, variables) => {
      const smsCount = calculateSmsCount(variables.smsData.message);
      toast({
        title: "SMS wysłany",
        description: `Wiadomość została wysłana na numer ${variables.smsData.to} (${smsCount} SMS)`,
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd wysyłania SMS",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const testConnectionMutation = useMutation({
    mutationFn: testConnection,
    onSuccess: (isConnected) => {
      if (isConnected) {
        toast({
          title: "Połączenie udane",
          description: "Połączenie z SMSAPI zostało nawiązane pomyślnie",
        });
      } else {
        toast({
          title: "Błąd połączenia",
          description: "Nie udało się połączyć z SMSAPI",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Błąd połączenia",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  return {
    balance: balance?.points || 0,
    balanceLoading,
    sendSms: sendSmsMutation.mutate,
    testConnection: testConnectionMutation.mutate,
    isSending: sendSmsMutation.isPending,
    isTesting: testConnectionMutation.isPending,
    calculateSmsCount,
    validateSmsMessage,
    replaceMessageVariables,
  };
};
