import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Schedule {
  id: string;
  user_id: string;
  customer_id: string;
  title: string;
  appointment_date: string;
  is_recurring: boolean;
  recurring_interval?: string;
  recurring_count: number;
  status: 'active' | 'completed' | 'cancelled';
  notes?: string;
  created_at: string;
  updated_at: string;
  customer?: {
    first_name?: string;
    last_name?: string;
    phone: string;
    address?: string;
  };
  notifications?: ScheduleNotification[];
}

export interface ScheduleNotification {
  id: string;
  schedule_id: string;
  notification_date: string;
  message_text: string;
  is_sent: boolean;
  sent_at?: string;
  sms_id?: string;
  error_message?: string;
  created_at: string;
}

export interface NewSchedule {
  customer_id: string;
  title: string;
  appointment_date: string;
  is_recurring?: boolean;
  recurring_interval?: string;
  recurring_count?: number;
  status?: string;
  notes?: string;
}

export interface NewScheduleNotification {
  schedule_id: string;
  notification_date: string;
  message_text: string;
}

// Fetch schedules with customer data and notifications
const fetchSchedules = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('schedules')
    .select(`
      *,
      customer:customers(first_name, last_name, phone, address),
      notifications:schedule_notifications(*)
    `)
    .eq('user_id', user.id)
    .order('appointment_date', { ascending: true });

  if (error) throw error;
  return data as Schedule[];
};

// Add schedule
const addSchedule = async (schedule: NewSchedule) => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('schedules')
    .insert({ ...schedule, user_id: user.id })
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Add schedule notification
const addScheduleNotification = async (notification: NewScheduleNotification) => {
  const { data, error } = await supabase
    .from('schedule_notifications')
    .insert(notification)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Update schedule
const updateSchedule = async (schedule: Partial<Schedule> & { id: string }) => {
  const { id, ...updateData } = schedule;
  const { data, error } = await supabase
    .from('schedules')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Delete schedule
const deleteSchedule = async (id: string) => {
  const { error } = await supabase
    .from('schedules')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
};

// Get pending notifications (for SMS sending)
const fetchPendingNotifications = async () => {
  const { data, error } = await supabase
    .from('schedule_notifications')
    .select(`
      *,
      schedule:schedules(
        *,
        customer:customers(*)
      )
    `)
    .eq('is_sent', false)
    .lte('notification_date', new Date().toISOString())
    .order('notification_date', { ascending: true });

  if (error) throw error;
  return data;
};

export const useSchedules = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: schedules, isLoading, isError } = useQuery({
    queryKey: ['schedules'],
    queryFn: fetchSchedules
  });

  const { data: pendingNotifications } = useQuery({
    queryKey: ['pending-notifications'],
    queryFn: fetchPendingNotifications,
    refetchInterval: 60000 // Check every minute
  });

  const addScheduleMutation = useMutation({
    mutationFn: addSchedule,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Termin dodany",
        description: `Termin "${data.title}" został dodany do harmonogramu.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się dodać terminu: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const addNotificationMutation = useMutation({
    mutationFn: addScheduleNotification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['pending-notifications'] });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się dodać powiadomienia: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const updateScheduleMutation = useMutation({
    mutationFn: updateSchedule,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Termin zaktualizowany",
        description: "Termin został pomyślnie zaktualizowany.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się zaktualizować terminu: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const deleteScheduleMutation = useMutation({
    mutationFn: deleteSchedule,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Termin usunięty",
        description: "Termin został usunięty z harmonogramu.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się usunąć terminu: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  return {
    schedules: schedules ?? [],
    pendingNotifications: pendingNotifications ?? [],
    isLoading,
    isError,
    addSchedule: addScheduleMutation.mutate,
    addNotification: addNotificationMutation.mutate,
    updateSchedule: updateScheduleMutation.mutate,
    deleteSchedule: deleteScheduleMutation.mutate,
  };
};
