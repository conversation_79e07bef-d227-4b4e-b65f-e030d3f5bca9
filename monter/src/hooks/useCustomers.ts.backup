
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';
import { useToast } from '@/hooks/use-toast';

export type Customer = Tables<'customers'>;
export type NewCustomer = TablesInsert<'customers'>;
export type UpdatedCustomer = TablesUpdate<'customers'>;

// Fetch customers
const fetchCustomers = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
};

// Add customer
const addCustomer = async (customer: NewCustomer) => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .insert({ ...customer, user_id: user.id })
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Update customer
const updateCustomer = async (customer: UpdatedCustomer & { id: string }) => {
    const { id, ...updateData } = customer;
    const { data, error } = await supabase
        .from('customers')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
    
    if (error) throw error;
    return data;
};

export const useCustomers = () => {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    const { data: customers, isLoading, isError } = useQuery({
        queryKey: ['customers'],
        queryFn: fetchCustomers
    });

    const addCustomerMutation = useMutation({
        mutationFn: addCustomer,
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: ['customers'] });
            toast({
                title: "Klient dodany",
                description: `${data.first_name} ${data.last_name} został dodany do bazy.`,
            });
        },
        onError: (error) => {
            toast({
                title: "Błąd",
                description: `Nie udało się dodać klienta: ${error.message}`,
                variant: "destructive",
            });
        }
    });

    const updateCustomerMutation = useMutation({
        mutationFn: updateCustomer,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['customers'] });
            toast({
                title: "Klient zaktualizowany",
                description: "Dane klienta zostały pomyślnie zaktualizowane.",
            });
        },
        onError: (error) => {
            toast({
                title: "Błąd",
                description: `Nie udało się zaktualizować klienta: ${error.message}`,
                variant: "destructive",
            });
        }
    });

    return {
        customers: customers ?? [],
        isLoading,
        isError,
        addCustomer: addCustomerMutation.mutate,
        updateCustomer: updateCustomerMutation.mutate,
    };
};
