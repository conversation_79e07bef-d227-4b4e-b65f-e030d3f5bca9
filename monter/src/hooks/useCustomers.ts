import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';
import { useToast } from '@/hooks/use-toast';

export type Customer = Tables<'customers'> & {
  company_name?: string;
  nip?: string;
  is_hidden?: boolean;
};

export type NewCustomer = TablesInsert<'customers'> & {
  company_name?: string;
  nip?: string;
  is_hidden?: boolean;
};

export type UpdatedCustomer = TablesUpdate<'customers'> & {
  company_name?: string;
  nip?: string;
  is_hidden?: boolean;
};

// Fetch customers (only visible ones)
const fetchCustomers = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_hidden', false)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as Customer[];
};

// Fetch hidden customers
const fetchHiddenCustomers = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_hidden', true)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as Customer[];
};

// Add customer
const addCustomer = async (customer: NewCustomer) => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .insert({ ...customer, user_id: user.id })
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Update customer
const updateCustomer = async (customer: UpdatedCustomer & { id: string }) => {
  const { id, ...updateData } = customer;
  const { data, error } = await supabase
    .from('customers')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Hide customer (soft delete)
const hideCustomer = async (id: string) => {
  const { data, error } = await supabase
    .from('customers')
    .update({ is_hidden: true })
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Restore customer
const restoreCustomer = async (id: string) => {
  const { data, error } = await supabase
    .from('customers')
    .update({ is_hidden: false })
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Delete customer permanently
const deleteCustomer = async (id: string) => {
  const { error } = await supabase
    .from('customers')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
};

// Search customers by phone or name
const searchCustomers = async (query: string) => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_hidden', false)
    .or(`phone.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%,company_name.ilike.%${query}%`)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as Customer[];
};

export const useCustomers = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: customers, isLoading, isError } = useQuery({
    queryKey: ['customers'],
    queryFn: fetchCustomers
  });

  const { data: hiddenCustomers } = useQuery({
    queryKey: ['hidden-customers'],
    queryFn: fetchHiddenCustomers
  });

  const addCustomerMutation = useMutation({
    mutationFn: addCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: "Klient dodany",
        description: "Nowy klient został dodany do bazy.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się dodać klienta: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const updateCustomerMutation = useMutation({
    mutationFn: updateCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: "Klient zaktualizowany",
        description: "Dane klienta zostały zaktualizowane.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się zaktualizować klienta: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const hideCustomerMutation = useMutation({
    mutationFn: hideCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['hidden-customers'] });
      toast({
        title: "Klient ukryty",
        description: "Klient został ukryty z listy. Możesz go przywrócić w sekcji ukrytych klientów.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się ukryć klienta: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const restoreCustomerMutation = useMutation({
    mutationFn: restoreCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['hidden-customers'] });
      toast({
        title: "Klient przywrócony",
        description: "Klient został przywrócony do listy aktywnych.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się przywrócić klienta: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const deleteCustomerMutation = useMutation({
    mutationFn: deleteCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['hidden-customers'] });
      toast({
        title: "Klient usunięty",
        description: "Klient został trwale usunięty z bazy danych.",
      });
    },
    onError: (error) => {
      toast({
        title: "Błąd",
        description: `Nie udało się usunąć klienta: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  return {
    customers: customers ?? [],
    hiddenCustomers: hiddenCustomers ?? [],
    isLoading,
    isError,
    addCustomer: addCustomerMutation.mutate,
    updateCustomer: updateCustomerMutation.mutate,
    hideCustomer: hideCustomerMutation.mutate,
    restoreCustomer: restoreCustomerMutation.mutate,
    deleteCustomer: deleteCustomerMutation.mutate,
    searchCustomers,
  };
};
