
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { NewCustomer } from '@/hooks/useCustomers';

interface CustomerFormProps {
    formData: Partial<NewCustomer>;
    onFormChange: (data: Partial<NewCustomer>) => void;
}

const CustomerForm = ({ formData, onFormChange }: CustomerFormProps) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        onFormChange({ ...formData, [e.target.id]: e.target.value });
    };

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="first_name">Imię</Label>
                    <Input
                        id="first_name"
                        value={formData.first_name || ''}
                        onChange={handleChange}
                        placeholder="Wprowadź imię"
                    />
                </div>
                <div>
                    <Label htmlFor="last_name">Nazwisko</Label>
                    <Input
                        id="last_name"
                        value={formData.last_name || ''}
                        onChange={handleChange}
                        placeholder="Wprowadź nazwisko"
                    />
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="phone">Telefon</Label>
                    <Input
                        id="phone"
                        value={formData.phone || ''}
                        onChange={handleChange}
                        placeholder="+48 123 456 789"
                    />
                </div>
                <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                        id="email"
                        type="email"
                        value={formData.email || ''}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                    />
                </div>
            </div>

            <div>
                <Label htmlFor="address">Adres</Label>
                <Input
                    id="address"
                    value={formData.address || ''}
                    onChange={handleChange}
                    placeholder="ul. Przykładowa 1, 00-000 Miasto"
                />
            </div>

            <div>
                <Label htmlFor="notes">Notatki</Label>
                <Textarea
                    id="notes"
                    value={formData.notes || ''}
                    onChange={handleChange}
                    placeholder="Dodatkowe informacje o kliencie lub instalacji..."
                    rows={3}
                />
            </div>
        </div>
    );
};

export default CustomerForm;
