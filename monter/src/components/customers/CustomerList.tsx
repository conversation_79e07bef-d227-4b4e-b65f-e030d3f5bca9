
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import CustomerListItem from './CustomerListItem';
import { Customer, UpdatedCustomer } from '@/hooks/useCustomers';

interface CustomerListProps {
    customers: Customer[];
    onUpdateCustomer: (customer: UpdatedCustomer & { id: string }) => void;
}

const CustomerList = ({ customers, onUpdateCustomer }: CustomerListProps) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Lista klientów</CardTitle>
                <CardDescription>
                    Zarządzaj swoimi klientami i ich danymi
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {customers.length > 0 ? (
                        customers.map((customer) => (
                            <CustomerListItem 
                                key={customer.id} 
                                customer={customer}
                                onUpdateCustomer={onUpdateCustomer}
                            />
                        ))
                    ) : (
                        <p className="text-center text-gray-500 py-8"><PERSON>rak klientów w bazie. <PERSON><PERSON>j pierwszego!</p>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default CustomerList;
