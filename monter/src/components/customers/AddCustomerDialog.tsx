import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import CustomerForm from './CustomerForm';
import { NewCustomer } from '@/hooks/useCustomers';

interface AddCustomerDialogProps {
    onAddCustomer: (customer: NewCustomer) => void;
}

const AddCustomerDialog = ({ onAddCustomer }: AddCustomerDialogProps) => {
    const [open, setOpen] = useState(false);
    const [formData, setFormData] = useState<Partial<NewCustomer>>({});
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    const validateForm = (data: Partial<NewCustomer>): Record<string, string> => {
        const newErrors: Record<string, string> = {};

        // Walidacja telefonu - wymagane
        if (!data.phone || data.phone.trim() === '') {
            newErrors.phone = 'Numer telefonu jest wymagany';
        } else {
            // Walidacja formatu telefonu
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,15}$/;
            const cleanPhone = data.phone.replace(/\s/g, '');
            if (!phoneRegex.test(cleanPhone)) {
                newErrors.phone = 'Nieprawidłowy format numeru telefonu';
            }
        }

        // Walidacja email jeśli podany
        if (data.email && data.email.trim() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                newErrors.email = 'Nieprawidłowy format adresu email';
            }
        }

        // Sprawdzenie czy przynajmniej imię lub nazwisko jest podane
        if ((!data.first_name || data.first_name.trim() === '') && 
            (!data.last_name || data.last_name.trim() === '')) {
            if (!newErrors.first_name) {
                newErrors.first_name = 'Podaj przynajmniej imię lub nazwisko';
            }
        }

        return newErrors;
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);
        const validationErrors = validateForm(formData);
        
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            setIsSubmitting(false);
            return;
        }

        try {
            // Przygotowanie danych - usunięcie pustych pól
            const cleanData: NewCustomer = {
                phone: formData.phone!.trim(),
                first_name: formData.first_name?.trim() || null,
                last_name: formData.last_name?.trim() || null,
                email: formData.email?.trim() || null,
                address: formData.address?.trim() || null,
                notes: formData.notes?.trim() || null,
            };

            onAddCustomer(cleanData);
            
            // Reset formularza
            setFormData({});
            setErrors({});
            setOpen(false);
        } catch (error) {
            console.error('Error adding customer:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleFormChange = (data: Partial<NewCustomer>) => {
        setFormData(data);
        // Wyczyść błędy dla zmienianych pól
        const newErrors = { ...errors };
        Object.keys(data).forEach(key => {
            if (newErrors[key]) {
                delete newErrors[key];
            }
        });
        setErrors(newErrors);
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Dodaj klienta
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle>Dodaj nowego klienta</DialogTitle>
                </DialogHeader>
                <CustomerForm 
                    formData={formData} 
                    onFormChange={handleFormChange}
                    errors={errors}
                />
                <div className="flex justify-end space-x-2 pt-4">
                    <Button 
                        variant="outline" 
                        onClick={() => setOpen(false)}
                        disabled={isSubmitting}
                    >
                        Anuluj
                    </Button>
                    <Button 
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="bg-blue-600 hover:bg-blue-700"
                    >
                        {isSubmitting ? 'Dodawanie...' : 'Dodaj klienta'}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default AddCustomerDialog;
