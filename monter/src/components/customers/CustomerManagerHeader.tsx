
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowUp } from 'lucide-react';
import AddCustomerDialog from './AddCustomerDialog';
import { NewCustomer } from '@/hooks/useCustomers';

interface CustomerManagerHeaderProps {
    onBack: () => void;
    onAddCustomer: (customer: NewCustomer) => void;
}

const CustomerManagerHeader = ({ onBack, onAddCustomer }: CustomerManagerHeaderProps) => {
    return (
        <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center space-x-4">
                        <Button variant="ghost" onClick={onBack}>
                            <ArrowUp className="w-4 h-4 mr-2" />
                            Powrót
                        </Button>
                        <h1 className="text-xl font-semibold text-gray-900">
                            Zarząd<PERSON><PERSON> klientami
                        </h1>
                    </div>
                    <AddCustomerDialog onAddCustomer={onAddCustomer} />
                </div>
            </div>
        </header>
    );
};

export default CustomerManagerHeader;
