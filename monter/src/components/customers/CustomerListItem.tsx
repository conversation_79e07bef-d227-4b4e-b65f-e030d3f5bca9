
import React from 'react';
import { Badge } from '@/components/ui/badge';
import EditCustomerDialog from './EditCustomerDialog';
import { Customer, UpdatedCustomer } from '@/hooks/useCustomers';

interface CustomerListItemProps {
    customer: Customer;
    onUpdateCustomer: (customer: UpdatedCustomer & { id: string }) => void;
}

const CustomerListItem = ({ customer, onUpdateCustomer }: CustomerListItemProps) => {
    return (
        <div className="border rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
                <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                            {customer.first_name} {customer.last_name}
                        </h3>
                        {customer.next_maintenance && (
                            <Badge variant="outline">
                                Przegląd: {customer.next_maintenance}
                            </Badge>
                        )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                            <p><strong>Telefon:</strong> {customer.phone}</p>
                            <p><strong>Email:</strong> {customer.email || '-'}</p>
                        </div>
                        <div>
                            <p><strong>Adres:</strong> {customer.address || '-'}</p>
                            {customer.last_maintenance && (
                                <p><strong>Ostatni przegląd:</strong> {customer.last_maintenance}</p>
                            )}
                        </div>
                    </div>
                    
                    {customer.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                            <strong>Notatki:</strong> {customer.notes}
                        </div>
                    )}
                </div>
                
                <div className="flex space-x-2">
                    <EditCustomerDialog customer={customer} onUpdateCustomer={onUpdateCustomer} />
                </div>
            </div>
        </div>
    );
};

export default CustomerListItem;
