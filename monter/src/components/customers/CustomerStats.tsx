
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { User, Mail } from 'lucide-react';
import { Customer } from '@/hooks/useCustomers';

interface CustomerStatsProps {
    customers: Customer[];
}

const CustomerStats = ({ customers }: CustomerStatsProps) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Łączna liczba klientów</p>
                            <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
                        </div>
                        <User className="w-8 h-8 text-blue-500" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Nadchodzące przeglądy</p>
                            <p className="text-2xl font-bold text-gray-900">
                                {customers.filter(c => c.next_maintenance).length}
                            </p>
                        </div>
                        <Mail className="w-8 h-8 text-green-500" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Aktywni klienci</p>
                            <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
                        </div>
                        <User className="w-8 h-8 text-purple-500" />
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default CustomerStats;
