import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { NewCustomer } from '@/hooks/useCustomers';
import { cn } from '@/lib/utils';

interface CustomerFormProps {
    formData: Partial<NewCustomer>;
    onFormChange: (data: Partial<NewCustomer>) => void;
    errors?: Record<string, string>;
}

const CustomerForm = ({ formData, onFormChange, errors = {} }: CustomerFormProps) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        onFormChange({ ...formData, [e.target.id]: e.target.value });
    };

    const validatePhone = (phone: string) => {
        // Podstawowa walidacja numeru telefonu
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,15}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    };

    const getFieldError = (fieldName: string) => {
        return errors[fieldName];
    };

    const hasError = (fieldName: string) => {
        return !!errors[fieldName];
    };

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="first_name" className="text-sm font-medium">
                        Imię <span className="text-gray-400">(opcjonalne)</span>
                    </Label>
                    <Input
                        id="first_name"
                        value={formData.first_name || ''}
                        onChange={handleChange}
                        placeholder="Wprowadź imię"
                        className={cn(
                            hasError('first_name') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('first_name') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('first_name')}</p>
                    )}
                </div>
                <div>
                    <Label htmlFor="last_name" className="text-sm font-medium">
                        Nazwisko <span className="text-gray-400">(opcjonalne)</span>
                    </Label>
                    <Input
                        id="last_name"
                        value={formData.last_name || ''}
                        onChange={handleChange}
                        placeholder="Wprowadź nazwisko"
                        className={cn(
                            hasError('last_name') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('last_name') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('last_name')}</p>
                    )}
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="phone" className="text-sm font-medium">
                        Telefon <span className="text-red-500">*</span>
                    </Label>
                    <Input
                        id="phone"
                        value={formData.phone || ''}
                        onChange={handleChange}
                        placeholder="+48 123 456 789"
                        className={cn(
                            hasError('phone') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('phone') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('phone')}</p>
                    )}
                    {!hasError('phone') && (
                        <p className="text-xs text-gray-500 mt-1">
                            Wymagane. Format: +48 123 456 789 lub 123456789
                        </p>
                    )}
                </div>
                <div>
                    <Label htmlFor="email" className="text-sm font-medium">
                        Email <span className="text-gray-400">(opcjonalne)</span>
                    </Label>
                    <Input
                        id="email"
                        type="email"
                        value={formData.email || ''}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                        className={cn(
                            hasError('email') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('email') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('email')}</p>
                    )}
                </div>
            </div>

            <div>
                <Label htmlFor="address" className="text-sm font-medium">
                    Adres <span className="text-gray-400">(opcjonalne)</span>
                </Label>
                <Input
                    id="address"
                    value={formData.address || ''}
                    onChange={handleChange}
                    placeholder="ul. Przykładowa 1, 00-000 Miasto"
                    className={cn(
                        hasError('address') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                    )}
                />
                {hasError('address') && (
                    <p className="text-sm text-red-600 mt-1">{getFieldError('address')}</p>
                )}
            </div>

            <div>
                <Label htmlFor="notes" className="text-sm font-medium">
                    Notatki <span className="text-gray-400">(opcjonalne)</span>
                </Label>
                <Textarea
                    id="notes"
                    value={formData.notes || ''}
                    onChange={handleChange}
                    placeholder="Dodatkowe informacje o kliencie..."
                    rows={3}
                    className={cn(
                        hasError('notes') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                    )}
                />
                {hasError('notes') && (
                    <p className="text-sm text-red-600 mt-1">{getFieldError('notes')}</p>
                )}
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                    <span className="font-medium">Wskazówka:</span> Wymagany jest tylko numer telefonu. 
                    Pozostałe pola są opcjonalne i można je wypełnić później.
                </p>
            </div>
        </div>
    );
};

export default CustomerForm;
