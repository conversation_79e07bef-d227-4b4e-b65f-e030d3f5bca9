
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus } from 'lucide-react';
import CustomerForm from './CustomerForm';
import { NewCustomer } from '@/hooks/useCustomers';

interface AddCustomerDialogProps {
    onAddCustomer: (customer: NewCustomer) => void;
}

const AddCustomerDialog = ({ onAddCustomer }: AddCustomerDialogProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState<Partial<NewCustomer>>({
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        address: '',
        notes: ''
    });

    const handleAdd = () => {
        if (formData.first_name && formData.last_name && formData.phone) {
            onAddCustomer(formData as NewCustomer);
            setIsOpen(false);
            setFormData({
                first_name: '',
                last_name: '',
                phone: '',
                email: '',
                address: '',
                notes: ''
            });
        }
    };
    
    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-blue-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Dodaj klienta
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>Dodaj nowego klienta</DialogTitle>
                    <DialogDescription>
                        Wprowadź dane nowego klienta do systemu
                    </DialogDescription>
                </DialogHeader>
                <CustomerForm formData={formData} onFormChange={setFormData} />
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Anuluj
                    </Button>
                    <Button onClick={handleAdd}>
                        Dodaj klienta
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default AddCustomerDialog;
