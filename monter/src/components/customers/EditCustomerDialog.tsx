
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Edit } from 'lucide-react';
import CustomerForm from './CustomerForm';
import { Customer, UpdatedCustomer } from '@/hooks/useCustomers';

interface EditCustomerDialogProps {
    customer: Customer;
    onUpdateCustomer: (customer: UpdatedCustomer & { id: string }) => void;
}

const EditCustomerDialog = ({ customer, onUpdateCustomer }: EditCustomerDialogProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState<Partial<UpdatedCustomer>>({});

    useEffect(() => {
        if (isOpen) {
            setFormData({
                first_name: customer.first_name,
                last_name: customer.last_name,
                phone: customer.phone,
                email: customer.email,
                address: customer.address,
                notes: customer.notes
            });
        }
    }, [isOpen, customer]);

    const handleUpdate = () => {
        onUpdateCustomer({ ...formData, id: customer.id });
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4" />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>Edytuj klienta</DialogTitle>
                    <DialogDescription>
                        Zaktualizuj dane klienta
                    </DialogDescription>
                </DialogHeader>
                <CustomerForm formData={formData} onFormChange={setFormData} />
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Anuluj
                    </Button>
                    <Button onClick={handleUpdate}>
                        Zapisz zmiany
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default EditCustomerDialog;
