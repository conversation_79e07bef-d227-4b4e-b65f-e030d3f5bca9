import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { NewCustomer } from '@/hooks/useCustomers';
import { cn } from '@/lib/utils';

interface CustomerFormProps {
    formData: Partial<NewCustomer>;
    onFormChange: (data: Partial<NewCustomer>) => void;
    errors?: Record<string, string>;
}

const CustomerForm = ({ formData, onFormChange, errors = {} }: CustomerFormProps) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        onFormChange({ ...formData, [e.target.id]: e.target.value });
    };

    const getFieldError = (fieldName: string) => {
        return errors[fieldName];
    };

    const hasError = (fieldName: string) => {
        return !!errors[fieldName];
    };

    return (
        <div className="space-y-6">
            {/* Instrukcje dla użytkownika */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">💡 Jak dodać klienta:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                    <li>• <strong>Wymagany jest tylko numer telefonu</strong> - pozostałe pola są opcjonalne</li>
                    <li>• Możesz dodać firmę lub osobę prywatną</li>
                    <li>• Dla firm: wypełnij "Nazwa firmy" i opcjonalnie NIP</li>
                    <li>• Dla osób: wypełnij imię i nazwisko</li>
                    <li>• Adres i email pomogą w lepszej organizacji</li>
                </ul>
            </div>

            {/* Sekcja Firma */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Dane firmy (opcjonalne)</h3>
                
                <div>
                    <Label htmlFor="company_name" className="text-sm font-medium">
                        Nazwa firmy
                    </Label>
                    <Input
                        id="company_name"
                        value={formData.company_name || ''}
                        onChange={handleChange}
                        placeholder="np. ABC Sp. z o.o., Salon Fryzjerski Anna"
                        className={cn(
                            hasError('company_name') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('company_name') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('company_name')}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                        Jeśli to firma, wpisz jej nazwę. Jeśli osoba prywatna, zostaw puste.
                    </p>
                </div>

                <div>
                    <Label htmlFor="nip" className="text-sm font-medium">
                        NIP
                    </Label>
                    <Input
                        id="nip"
                        value={formData.nip || ''}
                        onChange={handleChange}
                        placeholder="123-456-78-90 lub 1234567890"
                        className={cn(
                            hasError('nip') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('nip') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('nip')}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                        Numer NIP firmy (opcjonalnie)
                    </p>
                </div>
            </div>

            {/* Sekcja Dane osobowe */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Dane osobowe (opcjonalne)</h3>
                
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <Label htmlFor="first_name" className="text-sm font-medium">
                            Imię
                        </Label>
                        <Input
                            id="first_name"
                            value={formData.first_name || ''}
                            onChange={handleChange}
                            placeholder="Jan"
                            className={cn(
                                hasError('first_name') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                        {hasError('first_name') && (
                            <p className="text-sm text-red-600 mt-1">{getFieldError('first_name')}</p>
                        )}
                    </div>
                    <div>
                        <Label htmlFor="last_name" className="text-sm font-medium">
                            Nazwisko
                        </Label>
                        <Input
                            id="last_name"
                            value={formData.last_name || ''}
                            onChange={handleChange}
                            placeholder="Kowalski"
                            className={cn(
                                hasError('last_name') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                        {hasError('last_name') && (
                            <p className="text-sm text-red-600 mt-1">{getFieldError('last_name')}</p>
                        )}
                    </div>
                </div>
            </div>

            {/* Sekcja Kontakt */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Dane kontaktowe</h3>
                
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <Label htmlFor="phone" className="text-sm font-medium">
                            Telefon <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="phone"
                            value={formData.phone || ''}
                            onChange={handleChange}
                            placeholder="+48 123 456 789"
                            className={cn(
                                hasError('phone') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                        {hasError('phone') && (
                            <p className="text-sm text-red-600 mt-1">{getFieldError('phone')}</p>
                        )}
                        {!hasError('phone') && (
                            <p className="text-xs text-gray-500 mt-1">
                                <strong>Wymagane.</strong> Format: +48 123 456 789 lub 123456789
                            </p>
                        )}
                    </div>
                    <div>
                        <Label htmlFor="email" className="text-sm font-medium">
                            Email
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            value={formData.email || ''}
                            onChange={handleChange}
                            placeholder="<EMAIL>"
                            className={cn(
                                hasError('email') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                        {hasError('email') && (
                            <p className="text-sm text-red-600 mt-1">{getFieldError('email')}</p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                            Adres email do kontaktu (opcjonalnie)
                        </p>
                    </div>
                </div>

                <div>
                    <Label htmlFor="address" className="text-sm font-medium">
                        Adres
                    </Label>
                    <Input
                        id="address"
                        value={formData.address || ''}
                        onChange={handleChange}
                        placeholder="ul. Przykładowa 1, 00-000 Miasto"
                        className={cn(
                            hasError('address') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('address') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('address')}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                        Adres do wizyt serwisowych (opcjonalnie)
                    </p>
                </div>
            </div>

            {/* Sekcja Notatki */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Dodatkowe informacje</h3>
                
                <div>
                    <Label htmlFor="notes" className="text-sm font-medium">
                        Notatki
                    </Label>
                    <Textarea
                        id="notes"
                        value={formData.notes || ''}
                        onChange={handleChange}
                        placeholder="np. Preferowane godziny wizyt, specjalne wymagania, historia serwisu..."
                        rows={3}
                        className={cn(
                            hasError('notes') && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                    />
                    {hasError('notes') && (
                        <p className="text-sm text-red-600 mt-1">{getFieldError('notes')}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                        Wszelkie dodatkowe informacje o kliencie (opcjonalnie)
                    </p>
                </div>
            </div>

            {/* Podsumowanie */}
            <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                <p className="text-sm text-green-800">
                    <span className="font-medium">✓ Gotowe!</span> Wystarczy podać numer telefonu, aby dodać klienta. 
                    Pozostałe informacje możesz uzupełnić później w dowolnym momencie.
                </p>
            </div>
        </div>
    );
};

export default CustomerForm;
