import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, MessageSquare, User, MapPin, Edit, Trash2, Phone } from 'lucide-react';
import { useSchedules } from '@/hooks/useSchedules';
import { Loader2 } from 'lucide-react';

const ScheduleList = () => {
  const { schedules, isLoading, deleteSchedule } = useSchedules();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktywny';
      case 'completed': return '<PERSON>ako<PERSON><PERSON><PERSON>';
      case 'cancelled': return 'Anulowany';
      default: return 'Nieznany';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pl-PL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('pl-PL', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCustomerName = (customer: any) => {
    if (!customer) return 'Nieznany klient';
    
    const firstName = customer.first_name || '';
    const lastName = customer.last_name || '';
    
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    
    return 'Klient';
  };

  const handleDelete = (scheduleId: string, title: string) => {
    if (window.confirm(`Czy na pewno chcesz usunąć termin "${title}"?`)) {
      deleteSchedule(scheduleId);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        </CardContent>
      </Card>
    );
  }

  if (schedules.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Calendar className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Brak zaplanowanych terminów
          </h3>
          <p className="text-gray-500 text-center mb-4">
            Dodaj pierwszy termin, aby rozpocząć zarządzanie harmonogramem wizyt.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Zaplanowane terminy</h2>
        <div className="text-sm text-gray-500">
          {schedules.length} {schedules.length === 1 ? 'termin' : 'terminów'}
        </div>
      </div>

      {schedules.map((schedule) => (
        <Card key={schedule.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <CardTitle className="text-lg">{schedule.title}</CardTitle>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <User className="w-4 h-4" />
                    <span>{getCustomerName(schedule.customer)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Phone className="w-4 h-4" />
                    <span>{schedule.customer?.phone || 'Brak telefonu'}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(schedule.appointment_date)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{formatTime(schedule.appointment_date)}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(schedule.status)}>
                  {getStatusText(schedule.status)}
                </Badge>
                {schedule.is_recurring && (
                  <Badge variant="outline" className="text-xs">
                    Cykliczny
                  </Badge>
                )}
                <div className="flex space-x-1">
                  <Button variant="ghost" size="sm" title="Edytuj">
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    title="Usuń"
                    onClick={() => handleDelete(schedule.id, schedule.title)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {schedule.customer?.address && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span>{schedule.customer.address}</span>
                </div>
              )}

              {schedule.notes && (
                <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  <strong>Notatki:</strong> {schedule.notes}
                </div>
              )}

              <div className="border-t pt-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium flex items-center space-x-1">
                    <MessageSquare className="w-4 h-4" />
                    <span>Powiadomienia SMS</span>
                  </h4>
                  <span className="text-xs text-gray-500">
                    {schedule.notifications?.length || 0} zaplanowanych
                  </span>
                </div>
                
                {schedule.notifications && schedule.notifications.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {schedule.notifications.map((notification, index) => {
                      const notificationDate = new Date(notification.notification_date);
                      const appointmentDate = new Date(schedule.appointment_date);
                      const daysDiff = Math.ceil((appointmentDate.getTime() - notificationDate.getTime()) / (1000 * 60 * 60 * 24));
                      
                      return (
                        <div key={notification.id} className="bg-gray-50 rounded p-2 text-xs">
                          <div className="flex justify-between items-center">
                            <span>
                              {daysDiff === 0 
                                ? 'W dniu wizyty' 
                                : `${daysDiff} dni przed`
                              }
                            </span>
                            <span className="text-gray-500">
                              {formatTime(notification.notification_date)}
                            </span>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={`mt-1 text-xs ${
                              notification.is_sent 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}
                          >
                            {notification.is_sent ? 'Wysłane' : 'Oczekuje'}
                          </Badge>
                          {notification.error_message && (
                            <div className="text-red-600 text-xs mt-1">
                              Błąd: {notification.error_message}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-sm text-gray-500 italic">
                    Brak zaplanowanych powiadomień
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ScheduleList;
