import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Calendar, MessageSquare, Trash2 } from 'lucide-react';
import { Customer } from '@/hooks/useCustomers';
import { useSchedules, NewSchedule, NewScheduleNotification } from '@/hooks/useSchedules';
import { calculateSmsCount } from '@/hooks/useSmsApi';

interface AddScheduleDialogProps {
  customers: Customer[];
}

interface NotificationSettings {
  id: string;
  daysBeforeAppointment: number;
  time: string;
  message: string;
}

const AddScheduleDialog = ({ customers }: AddScheduleDialogProps) => {
  const [open, setOpen] = useState(false);
  const { addSchedule, addNotification } = useSchedules();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    customerId: '',
    title: '',
    appointmentDate: '',
    appointmentTime: '',
    isRecurring: false,
    recurringInterval: 'yearly',
    notes: '',
  });
  
  const [notifications, setNotifications] = useState<NotificationSettings[]>([
    {
      id: '1',
      daysBeforeAppointment: 7,
      time: '10:00',
      message: 'Przypominamy o wizycie za tydzień. Termin: {data} o {godzina}. Adres: {adres}.'
    }
  ]);

  const SMS_LIMIT = 160;

  const addNotificationSlot = () => {
    const newNotification: NotificationSettings = {
      id: Date.now().toString(),
      daysBeforeAppointment: 1,
      time: '10:00',
      message: 'Przypominamy o wizycie jutro. Termin: {data} o {godzina}.'
    };
    setNotifications([...notifications, newNotification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(notifications.filter(n => n.id !== id));
  };

  const updateNotification = (id: string, field: keyof NotificationSettings, value: string | number) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, [field]: value } : n
    ));
  };

  const validateForm = () => {
    if (!formData.customerId) {
      throw new Error('Wybierz klienta');
    }
    if (!formData.title.trim()) {
      throw new Error('Podaj tytuł wizyty');
    }
    if (!formData.appointmentDate) {
      throw new Error('Wybierz datę wizyty');
    }
    if (!formData.appointmentTime) {
      throw new Error('Wybierz godzinę wizyty');
    }
    
    // Validate notifications
    for (const notification of notifications) {
      if (!notification.message.trim()) {
        throw new Error('Wszystkie powiadomienia muszą mieć treść wiadomości');
      }
      if (notification.daysBeforeAppointment < 0) {
        throw new Error('Liczba dni przed wizytą nie może być ujemna');
      }
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      validateForm();
      
      // Create appointment datetime
      const appointmentDateTime = new Date(`${formData.appointmentDate}T${formData.appointmentTime}`);
      
      // Prepare schedule data
      const scheduleData: NewSchedule = {
        customer_id: formData.customerId,
        title: formData.title.trim(),
        appointment_date: appointmentDateTime.toISOString(),
        is_recurring: formData.isRecurring,
        recurring_interval: formData.isRecurring ? formData.recurringInterval : undefined,
        recurring_count: 1,
        status: 'active',
        notes: formData.notes.trim() || undefined,
      };

      // Add schedule first
      addSchedule(scheduleData, {
        onSuccess: (newSchedule) => {
          // Add notifications for this schedule
          notifications.forEach((notification) => {
            const notificationDateTime = new Date(appointmentDateTime);
            notificationDateTime.setDate(notificationDateTime.getDate() - notification.daysBeforeAppointment);
            
            // Set notification time
            const [hours, minutes] = notification.time.split(':');
            notificationDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            
            const notificationData: NewScheduleNotification = {
              schedule_id: newSchedule.id,
              notification_date: notificationDateTime.toISOString(),
              message_text: notification.message,
            };
            
            addNotification(notificationData);
          });
          
          // Reset form and close dialog
          setFormData({
            customerId: '',
            title: '',
            appointmentDate: '',
            appointmentTime: '',
            isRecurring: false,
            recurringInterval: 'yearly',
            notes: '',
          });
          setNotifications([{
            id: '1',
            daysBeforeAppointment: 7,
            time: '10:00',
            message: 'Przypominamy o wizycie za tydzień. Termin: {data} o {godzina}. Adres: {adres}.'
          }]);
          setOpen(false);
        }
      });
      
    } catch (error) {
      console.error('Error creating schedule:', error);
      // Error handling is done by the mutation
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedCustomer = () => {
    return customers.find(c => c.id === formData.customerId);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Dodaj termin
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>Dodaj nowy termin</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Informacje podstawowe</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customer">Klient *</Label>
                <Select value={formData.customerId} onValueChange={(value) => 
                  setFormData({...formData, customerId: value})
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Wybierz klienta" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.first_name || customer.last_name 
                          ? `${customer.first_name || ''} ${customer.last_name || ''}`.trim()
                          : 'Bez nazwy'
                        } - {customer.phone}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="title">Tytuł wizyty *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="np. Przegląd klimatyzacji, Wizyta kontrolna"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="appointmentDate">Data wizyty *</Label>
                <Input
                  id="appointmentDate"
                  type="date"
                  value={formData.appointmentDate}
                  onChange={(e) => setFormData({...formData, appointmentDate: e.target.value})}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div>
                <Label htmlFor="appointmentTime">Godzina wizyty *</Label>
                <Input
                  id="appointmentTime"
                  type="time"
                  value={formData.appointmentTime}
                  onChange={(e) => setFormData({...formData, appointmentTime: e.target.value})}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isRecurring"
                checked={formData.isRecurring}
                onCheckedChange={(checked) => 
                  setFormData({...formData, isRecurring: checked as boolean})
                }
              />
              <Label htmlFor="isRecurring">Termin cykliczny</Label>
            </div>

            {formData.isRecurring && (
              <div>
                <Label htmlFor="recurringInterval">Częstotliwość</Label>
                <Select value={formData.recurringInterval} onValueChange={(value) => 
                  setFormData({...formData, recurringInterval: value})
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yearly">Co rok</SelectItem>
                    <SelectItem value="monthly">Co miesiąc</SelectItem>
                    <SelectItem value="weekly">Co tydzień</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <Label htmlFor="notes">Notatki</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                placeholder="Dodatkowe informacje o wizycie..."
                rows={2}
              />
            </div>
          </div>

          {/* SMS Notifications */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <MessageSquare className="w-5 h-5" />
                <span>Powiadomienia SMS</span>
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addNotificationSlot}
              >
                <Plus className="w-4 h-4 mr-1" />
                Dodaj powiadomienie
              </Button>
            </div>

            {notifications.map((notification, index) => (
              <div key={notification.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Powiadomienie {index + 1}</h4>
                  {notifications.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeNotification(notification.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label>Ile dni przed wizytą</Label>
                    <Input
                      type="number"
                      min="0"
                      max="365"
                      value={notification.daysBeforeAppointment}
                      onChange={(e) => updateNotification(
                        notification.id, 
                        'daysBeforeAppointment', 
                        parseInt(e.target.value) || 0
                      )}
                    />
                  </div>

                  <div>
                    <Label>Godzina wysłania</Label>
                    <Input
                      type="time"
                      value={notification.time}
                      onChange={(e) => updateNotification(
                        notification.id, 
                        'time', 
                        e.target.value
                      )}
                    />
                  </div>
                </div>

                <div>
                  <Label>Treść wiadomości</Label>
                  <Textarea
                    value={notification.message}
                    onChange={(e) => updateNotification(notification.id, 'message', e.target.value)}
                    placeholder="Treść SMS..."
                    rows={3}
                  />
                  <div className="flex justify-between text-xs mt-1">
                    <span className="text-gray-500">
                      Dostępne zmienne: {'{data}'}, {'{godzina}'}, {'{adres}'}, {'{imie}'}
                    </span>
                    <span className={`${notification.message.length > SMS_LIMIT ? 'text-red-600' : 'text-gray-500'}`}>
                      {notification.message.length}/{SMS_LIMIT} znaków 
                      ({calculateSmsCount(notification.message)} SMS)
                    </span>
                  </div>
                  {notification.message.length > SMS_LIMIT && (
                    <p className="text-xs text-red-600 mt-1">
                      ⚠️ Wiadomość przekracza limit 160 znaków i zostanie wysłana jako {calculateSmsCount(notification.message)} SMS-y
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setOpen(false)} disabled={isSubmitting}>
              Anuluj
            </Button>
            <Button 
              onClick={handleSubmit} 
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Tworzenie...' : 'Utwórz termin'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddScheduleDialog;
