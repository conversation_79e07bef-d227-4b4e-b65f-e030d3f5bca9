import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, MessageSquare, User, MapPin, Edit, Trash2 } from 'lucide-react';

const ScheduleList = () => {
  // TODO: Replace with actual data from hook
  const schedules = [
    {
      id: '1',
      title: 'Przegląd klimatyzacji',
      customer: { name: '<PERSON>', phone: '+48 123 456 789' },
      appointmentDate: '2025-06-20',
      appointmentTime: '14:00',
      address: 'ul. Przykładowa 1, Warszawa',
      status: 'active',
      notifications: [
        { daysBeforeAppointment: 7, time: '10:00', status: 'pending' },
        { daysBeforeAppointment: 1, time: '10:00', status: 'pending' }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktywny';
      case 'completed': return 'Zakończony';
      case 'cancelled': return 'Anulowany';
      default: return 'Nieznany';
    }
  };

  if (schedules.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Calendar className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Brak zaplanowanych terminów
          </h3>
          <p className="text-gray-500 text-center mb-4">
            Dodaj pierwszy termin, aby rozpocząć zarządzanie harmonogramem wizyt.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Zaplanowane terminy</h2>
        <div className="text-sm text-gray-500">
          {schedules.length} {schedules.length === 1 ? 'termin' : 'terminów'}
        </div>
      </div>

      {schedules.map((schedule) => (
        <Card key={schedule.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <CardTitle className="text-lg">{schedule.title}</CardTitle>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <User className="w-4 h-4" />
                    <span>{schedule.customer.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(schedule.appointmentDate).toLocaleDateString('pl-PL')}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{schedule.appointmentTime}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(schedule.status)}>
                  {getStatusText(schedule.status)}
                </Badge>
                <div className="flex space-x-1">
                  <Button variant="ghost" size="sm">
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>{schedule.address}</span>
              </div>

              <div className="border-t pt-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium flex items-center space-x-1">
                    <MessageSquare className="w-4 h-4" />
                    <span>Powiadomienia SMS</span>
                  </h4>
                  <span className="text-xs text-gray-500">
                    {schedule.notifications.length} zaplanowanych
                  </span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {schedule.notifications.map((notification, index) => (
                    <div key={index} className="bg-gray-50 rounded p-2 text-xs">
                      <div className="flex justify-between items-center">
                        <span>
                          {notification.daysBeforeAppointment === 0 
                            ? 'W dniu wizyty' 
                            : `${notification.daysBeforeAppointment} dni przed`
                          }
                        </span>
                        <span className="text-gray-500">{notification.time}</span>
                      </div>
                      <Badge 
                        variant="outline" 
                        className="mt-1 text-xs"
                      >
                        {notification.status === 'pending' ? 'Oczekuje' : 'Wysłane'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ScheduleList;
