
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, Lock, User as UserIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

const SignUpForm = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
        },
        emailRedirectTo: `${window.location.origin}/`,
      },
    });

    if (error) {
      toast({
        title: "Błąd rejestracji",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Rejestracja pomyślna",
        description: "Sprawdź swoją skrzynkę e-mail, aby potwierdzić konto.",
      });
    }
    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSignUp} className="space-y-4 pt-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">Imię</Label>
          <div className="relative">
            <UserIcon className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input id="firstName" value={firstName} onChange={e => setFirstName(e.target.value)} placeholder="Jan" required className="pl-10"/>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Nazwisko</Label>
          <div className="relative">
            <UserIcon className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input id="lastName" value={lastName} onChange={e => setLastName(e.target.value)} placeholder="Kowalski" required className="pl-10"/>
          </div>
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="email-signup">Email</Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input id="email-signup" type="email" placeholder="<EMAIL>" value={email} onChange={e => setEmail(e.target.value)} required className="pl-10"/>
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="password-signup">Hasło</Label>
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input id="password-signup" type="password" placeholder="Min. 6 znaków" value={password} onChange={e => setPassword(e.target.value)} required className="pl-10"/>
        </div>
      </div>
      <Button type="submit" className="w-full h-12 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg" disabled={isLoading}>
        {isLoading ? 'Rejestrowanie...' : 'Zarejestruj się'}
      </Button>
    </form>
  );
};

export default SignUpForm;
