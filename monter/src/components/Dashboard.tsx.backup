
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Calendar, Mail, Settings, Database, User, Loader2, UserCheck } from 'lucide-react';
import { useDashboardStats } from '@/hooks/useDashboardStats';

interface DashboardProps {
  userRole: 'admin' | 'monter';
  onNavigate: (page: string) => void;
  onLogout: () => void;
}

const Dashboard = ({ userRole, onNavigate, onLogout }: DashboardProps) => {
  const { data: stats, isLoading } = useDashboardStats();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="h-16 w-16 animate-spin text-blue-600" />
      </div>
    );
  }

  const dashboardStats = [
    { title: '<PERSON><PERSON> klienci', value: stats?.customersCount?.toString() || '0', icon: Users, color: 'bg-blue-500' },
    { title: 'Nadchodzące przeglądy', value: stats?.upcomingMaintenanceCount?.toString() || '0', icon: Calendar, color: 'bg-green-500' },
    { title: 'SMS wysłane dziś', value: stats?.todaysSmsCount?.toString() || '0', icon: Mail, color: 'bg-purple-500' },
    { title: 'Status konta', value: stats?.profile?.is_active ? 'Aktywne' : 'Nieaktywne', icon: Settings, color: stats?.profile?.is_active ? 'bg-green-500' : 'bg-red-500' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                <Settings className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                TerminSMS
              </h1>
              <Badge variant={userRole === 'admin' ? 'default' : 'secondary'}>
                {userRole === 'admin' ? 'Administrator' : 'Monter'}
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              {stats?.profile && (
                <span className="text-sm text-gray-600">
                  Witaj, {stats.profile.first_name} {stats.profile.last_name}!
                </span>
              )}
              <Button variant="outline" onClick={onLogout}>
                <User className="w-4 h-4 mr-2" />
                Wyloguj
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Witaj w panelu {userRole === 'admin' ? 'administratora' : 'montera'}!
          </h2>
          <p className="text-gray-600">
            {userRole === 'admin' 
              ? 'Zarządzaj całym systemem i monitoruj statystyki'
              : 'Zarządzaj swoimi klientami i harmonogramami przeglądów'
            }
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Szybkie akcje</CardTitle>
              <CardDescription>
                Najczęściej używane funkcje
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={() => onNavigate('customers')} 
                className="w-full justify-start h-12"
                variant="outline"
              >
                <Users className="w-4 h-4 mr-3" />
                Zarządzaj klientami
              </Button>
              <Button 
                onClick={() => onNavigate('schedules')} 
                className="w-full justify-start h-12"
                variant="outline"
              >
                <Calendar className="w-4 h-4 mr-3" />
                Harmonogramy przeglądów
              </Button>
              <Button 
                onClick={() => onNavigate('templates')} 
                className="w-full justify-start h-12"
                variant="outline"
              >
                <Mail className="w-4 h-4 mr-3" />
                Szablony wiadomości
              </Button>
              {userRole === 'admin' ? (
                <Button 
                  onClick={() => onNavigate('monters')} 
                  className="w-full justify-start h-12"
                  variant="outline"
                >
                  <UserCheck className="w-4 h-4 mr-3" />
                  Zarządzanie monterami
                </Button>
              ) : (
                <Button 
                  onClick={() => onNavigate('smsapi')} 
                  className="w-full justify-start h-12"
                  variant="outline"
                >
                  <Settings className="w-4 h-4 mr-3" />
                  Konfiguracja SMSAPI
                </Button>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ostatnie aktywności</CardTitle>
              <CardDescription>
                Najnowsze zdarzenia w systemie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">Zalogowano do systemu</span>
                  <span className="text-gray-400 ml-auto">Teraz</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">Połączono z bazą danych</span>
                  <span className="text-gray-400 ml-auto">1 min temu</span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600">System gotowy do pracy</span>
                  <span className="text-gray-400 ml-auto">1 min temu</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
