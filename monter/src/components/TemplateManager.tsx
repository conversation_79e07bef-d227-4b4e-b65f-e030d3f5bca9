
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, MessageSquare, Plus } from 'lucide-react';

interface TemplateManagerProps {
  onBack: () => void;
}

const TemplateManager = ({ onBack }: TemplateManagerProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Powrót
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                <PERSON><PERSON><PERSON><PERSON><PERSON> wiadomo<PERSON>ci
              </h1>
            </div>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              Dodaj szablon
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="w-5 h-5 mr-2" />
              Twoje szablony
            </CardTitle>
            <CardDescription>
              Zarządzaj szablonami wiadomości SMS dla Twoich klientów
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center text-gray-500 py-8">
              Brak szablonów wiadomości. Dodaj pierwszy szablon!
            </p>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default TemplateManager;
