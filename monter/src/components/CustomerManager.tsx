
import React from 'react';
import { useCustomers } from '@/hooks/useCustomers';
import CustomerManagerHeader from './customers/CustomerManagerHeader';
import CustomerStats from './customers/CustomerStats';
import CustomerList from './customers/CustomerList';
import { Loader2 } from 'lucide-react';

interface CustomerManagerProps {
  onBack: () => void;
}

const CustomerManager = ({ onBack }: CustomerManagerProps) => {
  const { customers, isLoading, addCustomer, updateCustomer } = useCustomers();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="h-16 w-16 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <CustomerManagerHeader onBack={onBack} onAddCustomer={addCustomer} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CustomerStats customers={customers} />
        <CustomerList customers={customers} onUpdateCustomer={updateCustomer} />
      </main>
    </div>
  );
};

export default CustomerManager;
