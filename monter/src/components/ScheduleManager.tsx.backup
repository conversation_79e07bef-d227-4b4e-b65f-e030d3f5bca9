
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock } from 'lucide-react';

interface ScheduleManagerProps {
  onBack: () => void;
}

const ScheduleManager = ({ onBack }: ScheduleManagerProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Powrót
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                Harmonogramy przeglądów
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Nadchodzące przeglądy
              </CardTitle>
              <CardDescription>
                Lista zaplanowanych przeglądów dla Twoich klientów
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-500 py-8">
                Brak zaplanowanych przeglądów. Dodaj daty przeglądów w zarządzaniu klientami.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Historia przeglądów
              </CardTitle>
              <CardDescription>
                Przeglądy wykonane w ostatnim czasie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-500 py-8">
                Brak historii przeglądów.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default ScheduleManager;
