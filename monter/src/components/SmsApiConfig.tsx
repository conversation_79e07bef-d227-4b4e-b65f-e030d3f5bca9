
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>, Shield } from 'lucide-react';

interface SmsApiConfigProps {
  onBack: () => void;
}

const SmsApiConfig = ({ onBack }: SmsApiConfigProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Powrót
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                Konfiguracja SMSAPI
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Token SMSAPI
              </CardTitle>
              <CardDescription>
                Skonfiguruj swój token SMSAPI do wysyłania wiadomości SMS
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="smsapi_token">Token SMSAPI</Label>
                <Input
                  id="smsapi_token"
                  type="password"
                  placeholder="Wprowadź swój token SMSAPI"
                />
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Jak uzyskać token SMSAPI?</h4>
                <p className="text-sm text-blue-700">
                  1. Zarejestruj się na smsapi.pl<br/>
                  2. Przejdź do sekcji "API"<br/>
                  3. Wygeneruj nowy token<br/>
                  4. Skopiuj i wklej tutaj
                </p>
              </div>
              <Button className="w-full">
                <Settings className="w-4 h-4 mr-2" />
                Zapisz konfigurację
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default SmsApiConfig;
