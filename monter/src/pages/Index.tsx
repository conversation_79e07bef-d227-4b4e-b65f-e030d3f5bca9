import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Session } from '@supabase/supabase-js';
import { useNavigate } from 'react-router-dom';
import Dashboard from '@/components/Dashboard';
import CustomerManager from '@/components/CustomerManager';
import ScheduleManager from '@/components/ScheduleManager';
import TemplateManager from '@/components/TemplateManager';
import SmsApiConfig from '@/components/SmsApiConfig';
import MonterManager from '@/components/MonterManager';
import { useUserRole } from '@/hooks/useUserRole';
import { Loader2 } from 'lucide-react';

const Index = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const navigate = useNavigate();
  const { userRole, loading: roleLoading } = useUserRole(session?.user || null);

  useEffect(() => {
    setLoading(true);
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        navigate('/auth');
      }
      setLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [navigate]);

  const handleLogout = async () => {
    console.log('User logging out');
    await supabase.auth.signOut();
  };

  const handleNavigate = (page: string) => {
    console.log('Navigating to page:', page);
    setCurrentPage(page);
  };

  const handleBack = () => {
    setCurrentPage('dashboard');
  };

  if (loading || roleLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="h-16 w-16 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!session) {
    // Should be redirected by the effect, this is a fallback.
    return null;
  }

  switch (currentPage) {
    case 'customers':
      return <CustomerManager onBack={handleBack} />;
    case 'schedules':
      return <ScheduleManager onBack={handleBack} />;
    case 'templates':
      return <TemplateManager onBack={handleBack} />;
    case 'smsapi':
      return <SmsApiConfig onBack={handleBack} />;
    case 'monters':
      return <MonterManager onBack={handleBack} />;
    case 'dashboard':
    default:
      return (
        <Dashboard 
          userRole={userRole} 
          onNavigate={handleNavigate} 
          onLogout={handleLogout} 
        />
      );
  }
};

export default Index;
