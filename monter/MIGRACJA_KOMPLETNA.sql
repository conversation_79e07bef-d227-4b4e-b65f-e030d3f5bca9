-- =====================================================
-- KOMPLETNA MIGRACJA BAZY DANYCH - TerminSMS
-- Wykonaj ten skrypt w Supabase SQL Editor
-- =====================================================

-- 1. DODANIE KOLUMNY ROLE DO PROFILES
-- =====================================================
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'role') THEN
        ALTER TABLE public.profiles 
        ADD COLUMN role text DEFAULT 'monter' CHECK (role IN ('admin', 'monter'));
    END IF;
END $$;

-- 2. AKTUALIZACJA FUNKCJI HANDLE_NEW_USER
-- =====================================================
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, role)
  VALUES (
    new.id, 
    new.raw_user_meta_data ->> 'first_name', 
    new.raw_user_meta_data ->> 'last_name',
    COALESCE(new.raw_user_meta_data ->> 'role', 'monter')
  );
  RETURN new;
END;
$$;

-- 3. FUNKCJA SPRAWDZANIA ADMINISTRATORA
-- =====================================================
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND role = 'admin'
  );
END;
$$;

-- 4. ZMIANA STRUKTURY TABELI CUSTOMERS
-- =====================================================
-- Pola opcjonalne
DO $$ 
BEGIN
    -- Sprawdź czy kolumny są NOT NULL i zmień je
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'customers' AND column_name = 'first_name' AND is_nullable = 'NO') THEN
        ALTER TABLE public.customers ALTER COLUMN first_name DROP NOT NULL;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'customers' AND column_name = 'last_name' AND is_nullable = 'NO') THEN
        ALTER TABLE public.customers ALTER COLUMN last_name DROP NOT NULL;
    END IF;
END $$;

-- Dodanie nowych pól do customers
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'customers' AND column_name = 'company_name') THEN
        ALTER TABLE public.customers ADD COLUMN company_name text;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'customers' AND column_name = 'nip') THEN
        ALTER TABLE public.customers ADD COLUMN nip text;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'customers' AND column_name = 'is_hidden') THEN
        ALTER TABLE public.customers ADD COLUMN is_hidden boolean DEFAULT false;
    END IF;
END $$;

-- 5. TABELA HARMONOGRAMÓW
-- =====================================================
CREATE TABLE IF NOT EXISTS public.schedules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  customer_id uuid NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  title text NOT NULL,
  appointment_date timestamp with time zone NOT NULL,
  is_recurring boolean DEFAULT false,
  recurring_interval text,
  recurring_count integer DEFAULT 1,
  status text DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- 6. TABELA POWIADOMIEŃ SMS
-- =====================================================
CREATE TABLE IF NOT EXISTS public.schedule_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  schedule_id uuid NOT NULL REFERENCES public.schedules(id) ON DELETE CASCADE,
  notification_date timestamp with time zone NOT NULL,
  message_text text NOT NULL,
  is_sent boolean DEFAULT false,
  sent_at timestamp with time zone,
  sms_id text,
  error_message text,
  created_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- 7. TABELA FAKTUR
-- =====================================================
CREATE TABLE IF NOT EXISTS public.invoices (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  invoice_number text NOT NULL,
  invoice_date date NOT NULL,
  amount decimal(10,2) NOT NULL,
  currency text DEFAULT 'PLN',
  status text DEFAULT 'unpaid' CHECK (status IN ('paid', 'unpaid')),
  file_url text,
  file_name text,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- 8. DODANIE POLA SUBSCRIPTION_END DO PROFILES
-- =====================================================
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'subscription_end') THEN
        ALTER TABLE public.profiles 
        ADD COLUMN subscription_end date DEFAULT (now() + interval '1 year');
    END IF;
END $$;

-- 9. INDEKSY DLA WYDAJNOŚCI
-- =====================================================
-- Indeksy dla schedules
CREATE INDEX IF NOT EXISTS idx_schedules_user_id ON public.schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_schedules_customer_id ON public.schedules(customer_id);
CREATE INDEX IF NOT EXISTS idx_schedules_appointment_date ON public.schedules(appointment_date);

-- Indeksy dla schedule_notifications
CREATE INDEX IF NOT EXISTS idx_schedule_notifications_schedule_id ON public.schedule_notifications(schedule_id);
CREATE INDEX IF NOT EXISTS idx_schedule_notifications_date ON public.schedule_notifications(notification_date);
CREATE INDEX IF NOT EXISTS idx_schedule_notifications_pending ON public.schedule_notifications(notification_date) WHERE is_sent = false;

-- Indeksy dla invoices
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON public.invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON public.invoices(invoice_date);

-- 10. POLITYKI RLS
-- =====================================================
-- Włączenie RLS dla nowych tabel
ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schedule_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- Polityki dla schedules
DROP POLICY IF EXISTS "Users can view own schedules" ON public.schedules;
DROP POLICY IF EXISTS "Users can insert own schedules" ON public.schedules;
DROP POLICY IF EXISTS "Users can update own schedules" ON public.schedules;
DROP POLICY IF EXISTS "Users can delete own schedules" ON public.schedules;

CREATE POLICY "Users can view own schedules" ON public.schedules
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own schedules" ON public.schedules
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own schedules" ON public.schedules
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own schedules" ON public.schedules
  FOR DELETE USING (auth.uid() = user_id);

-- Polityki dla schedule_notifications
DROP POLICY IF EXISTS "Users can view own schedule notifications" ON public.schedule_notifications;
DROP POLICY IF EXISTS "Users can insert own schedule notifications" ON public.schedule_notifications;
DROP POLICY IF EXISTS "Users can update own schedule notifications" ON public.schedule_notifications;

CREATE POLICY "Users can view own schedule notifications" ON public.schedule_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own schedule notifications" ON public.schedule_notifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own schedule notifications" ON public.schedule_notifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

-- Polityki dla invoices
DROP POLICY IF EXISTS "Users can view own invoices" ON public.invoices;
DROP POLICY IF EXISTS "Admins can manage all invoices" ON public.invoices;

CREATE POLICY "Users can view own invoices" ON public.invoices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all invoices" ON public.invoices
  FOR ALL USING (public.is_admin(auth.uid()));

-- 11. DODANIE PROFILU DLA ISTNIEJĄCEGO UŻYTKOWNIKA
-- =====================================================
INSERT INTO public.profiles (id, first_name, last_name, role)
VALUES (
  '633e66aa-155b-4ef1-82f0-87ac8746fd2d',
  'Admin',
  'System', 
  'admin'
) ON CONFLICT (id) DO UPDATE SET 
  role = 'admin',
  first_name = COALESCE(EXCLUDED.first_name, profiles.first_name),
  last_name = COALESCE(EXCLUDED.last_name, profiles.last_name);

-- =====================================================
-- KONIEC MIGRACJI
-- =====================================================
-- Sprawdzenie czy wszystko zostało utworzone
SELECT 'Migracja zakończona pomyślnie!' as status;
