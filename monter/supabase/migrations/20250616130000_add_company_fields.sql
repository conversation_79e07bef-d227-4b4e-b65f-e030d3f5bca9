-- <PERSON><PERSON><PERSON> pól firma i NIP do tabeli customers
ALTER TABLE public.customers 
ADD COLUMN company_name text,
ADD COLUMN nip text;

-- <PERSON><PERSON><PERSON> pola is_hidden do ukrywania klientów
ALTER TABLE public.customers 
ADD COLUMN is_hidden boolean DEFAULT false;

-- <PERSON><PERSON><PERSON> tabeli faktur
CREATE TABLE public.invoices (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  invoice_number text NOT NULL,
  invoice_date date NOT NULL,
  amount decimal(10,2) NOT NULL,
  currency text DEFAULT 'PLN',
  status text DEFAULT 'unpaid' CHECK (status IN ('paid', 'unpaid')),
  file_url text,
  file_name text,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- Indeksy dla faktur
CREATE INDEX idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX idx_invoices_status ON public.invoices(status);
CREATE INDEX idx_invoices_date ON public.invoices(invoice_date);

-- Polityki RLS dla faktur
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own invoices" ON public.invoices
  FOR SELECT USING (auth.uid() = user_id);

-- Admins can manage all invoices
CREATE POLICY "Admins can manage all invoices" ON public.invoices
  FOR ALL USING (public.is_admin(auth.uid()));

-- Dodanie pola subscription_end do profiles
ALTER TABLE public.profiles 
ADD COLUMN subscription_end date DEFAULT (now() + interval '1 year');
