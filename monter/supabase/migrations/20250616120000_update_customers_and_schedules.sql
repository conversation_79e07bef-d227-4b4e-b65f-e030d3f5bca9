-- <PERSON><PERSON><PERSON> struktury tabeli customers - pola opcjonalne
ALTER TABLE public.customers 
ALTER COLUMN first_name DROP NOT NULL,
ALTER COLUMN last_name DROP NOT NULL;

-- <PERSON><PERSON>ie tabeli harmonogramów/terminów
CREATE TABLE public.schedules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  customer_id uuid NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  title text NOT NULL,
  appointment_date timestamp with time zone NOT NULL,
  is_recurring boolean DEFAULT false,
  recurring_interval text, -- 'yearly', 'monthly', 'weekly'
  recurring_count integer DEFAULT 1,
  status text DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- <PERSON><PERSON><PERSON> powiadomie<PERSON> SMS dla harmonogramów
CREATE TABLE public.schedule_notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  schedule_id uuid NOT NULL REFERENCES public.schedules(id) ON DELETE CASCADE,
  notification_date timestamp with time zone NOT NULL,
  message_text text NOT NULL,
  is_sent boolean DEFAULT false,
  sent_at timestamp with time zone,
  sms_id text, -- ID z SMSAPI
  error_message text,
  created_at timestamp with time zone DEFAULT now(),
  PRIMARY KEY (id)
);

-- Indeksy dla wydajności
CREATE INDEX idx_schedules_user_id ON public.schedules(user_id);
CREATE INDEX idx_schedules_customer_id ON public.schedules(customer_id);
CREATE INDEX idx_schedules_appointment_date ON public.schedules(appointment_date);
CREATE INDEX idx_schedule_notifications_schedule_id ON public.schedule_notifications(schedule_id);
CREATE INDEX idx_schedule_notifications_date ON public.schedule_notifications(notification_date);
CREATE INDEX idx_schedule_notifications_pending ON public.schedule_notifications(notification_date) WHERE is_sent = false;

-- Polityki RLS dla schedules
ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own schedules" ON public.schedules
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own schedules" ON public.schedules
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own schedules" ON public.schedules
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own schedules" ON public.schedules
  FOR DELETE USING (auth.uid() = user_id);

-- Polityki RLS dla schedule_notifications
ALTER TABLE public.schedule_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own schedule notifications" ON public.schedule_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own schedule notifications" ON public.schedule_notifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own schedule notifications" ON public.schedule_notifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.schedules 
      WHERE schedules.id = schedule_notifications.schedule_id 
      AND schedules.user_id = auth.uid()
    )
  );

-- Funkcja do automatycznego tworzenia powiadomień
CREATE OR REPLACE FUNCTION public.create_schedule_notifications()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Ta funkcja będzie rozszerzona w kodzie aplikacji
  -- do tworzenia konkretnych powiadomień
  RETURN NEW;
END;
$$;

-- Trigger do automatycznego tworzenia powiadomień
CREATE TRIGGER on_schedule_created
  AFTER INSERT ON public.schedules
  FOR EACH ROW EXECUTE PROCEDURE public.create_schedule_notifications();
