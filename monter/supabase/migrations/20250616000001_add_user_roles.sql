-- <PERSON><PERSON><PERSON> kolumny role do tabeli profiles
ALTER TABLE public.profiles 
ADD COLUMN role text DEFAULT 'monter' CHECK (role IN ('admin', 'monter'));

-- Aktualizacja funkcji handle_new_user aby ustawiała domyśln<PERSON> rolę
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, role)
  VALUES (
    new.id, 
    new.raw_user_meta_data ->> 'first_name', 
    new.raw_user_meta_data ->> 'last_name',
    COALESCE(new.raw_user_meta_data ->> 'role', 'monter')
  );
  RETURN new;
END;
$$;

-- Funkcja do sprawdzania czy użytkownik jest administratorem
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND role = 'admin'
  );
END;
$$;

-- Polityka RLS pozwalająca adminom na zarządzanie wszystkimi profilami
CREATE POLICY "Admins can manage all profiles" ON public.profiles
  FOR ALL USING (
    auth.uid() = id OR 
    public.is_admin(auth.uid())
  );

-- Polityka RLS pozwalająca adminom na zarządzanie wszystkimi klientami
CREATE POLICY "Admins can manage all customers" ON public.customers
  FOR ALL USING (
    auth.uid() = user_id OR 
    public.is_admin(auth.uid())
  );

-- Polityka RLS pozwalająca adminom na zarządzanie wszystkimi szablonami
CREATE POLICY "Admins can manage all templates" ON public.message_templates
  FOR ALL USING (
    auth.uid() = user_id OR 
    public.is_admin(auth.uid())
  );

-- Polityka RLS pozwalająca adminom na dostęp do całej historii SMS
CREATE POLICY "Admins can view all sms history" ON public.sms_history
  FOR ALL USING (
    auth.uid() = user_id OR 
    public.is_admin(auth.uid())
  );
