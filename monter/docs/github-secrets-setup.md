# 🔐 Konfigu<PERSON>ja GitHub Secrets

## 📋 Wymagane Secrets

Aby automatyczny deployment d<PERSON><PERSON><PERSON>, mus<PERSON><PERSON> skonfigurować następujące secrets w repozytorium GitHub.

### Jak dodać Secrets?

1. <PERSON><PERSON><PERSON><PERSON><PERSON> do repozytorium na GitHub
2. K<PERSON>nij **Settings** (zakładka)
3. W menu bocznym wybierz **Secrets and variables** > **Actions**
4. K<PERSON>nij **New repository secret**
5. Dodaj każdy secret z listy poniżej

## 🔑 Lista Required Secrets

### VPS_HOST
- **Nazwa**: `VPS_HOST`
- **War<PERSON><PERSON>ć**: IP adres Twojego VPS
- **Przykład**: `*************`

### VPS_USER
- **Nazwa**: `VPS_USER`
- **Wartość**: Nazwa użytkownika na VPS
- **Przykład**: `ubuntu`

### VPS_SSH_KEY
- **Nazwa**: `VPS_SSH_KEY`
- **<PERSON><PERSON><PERSON><PERSON>**: <PERSON>lucz prywatny SSH (cały plik)
- **J<PERSON>**: <PERSON>ob<PERSON><PERSON> sekcję "Generowanie klucza SSH" poniżej

### VITE_SUPABASE_URL
- **Nazwa**: `VITE_SUPABASE_URL`
- **Wartość**: URL Twojego projektu Supabase
- **Przykład**: `https://abcdefghijklmnop.supabase.co`
- **Gdzie znaleźć**: Supabase Dashboard > Settings > API

### VITE_SUPABASE_ANON_KEY
- **Nazwa**: `VITE_SUPABASE_ANON_KEY`
- **Wartość**: Publiczny klucz API Supabase
- **Przykład**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Gdzie znaleźć**: Supabase Dashboard > Settings > API

## 🔐 Generowanie klucza SSH

### Krok 1: Połącz się z VPS
```bash
ssh ubuntu@YOUR_VPS_IP
```

### Krok 2: Wygeneruj klucz SSH dla GitHub Actions
```bash
ssh-keygen -t ed25519 -C "github-actions" -f ~/.ssh/github-actions
```

**Uwaga**: Nie ustawiaj hasła (naciśnij Enter gdy zostaniesz zapytany)

### Krok 3: Dodaj klucz publiczny do authorized_keys
```bash
cat ~/.ssh/github-actions.pub >> ~/.ssh/authorized_keys
```

### Krok 4: Skopiuj klucz prywatny
```bash
cat ~/.ssh/github-actions
```

**Skopiuj całą zawartość** (włącznie z `-----BEGIN OPENSSH PRIVATE KEY-----` i `-----END OPENSSH PRIVATE KEY-----`) i wklej jako wartość `VPS_SSH_KEY` w GitHub Secrets.

### Krok 5: Test klucza (opcjonalnie)
```bash
# Na lokalnej maszynie, zapisz klucz do pliku i przetestuj
ssh -i /path/to/private-key ubuntu@YOUR_VPS_IP
```

## 🔍 Weryfikacja Secrets

Po dodaniu wszystkich secrets, sprawdź czy są poprawnie skonfigurowane:

### 1. Lista Secrets
W GitHub > Settings > Secrets and variables > Actions powinieneś zobaczyć:
- ✅ VPS_HOST
- ✅ VPS_USER  
- ✅ VPS_SSH_KEY
- ✅ VITE_SUPABASE_URL
- ✅ VITE_SUPABASE_ANON_KEY

### 2. Test połączenia SSH
Uruchom test workflow lub wykonaj commit do main branch i sprawdź czy deployment się powiedzie.

## 🚨 Bezpieczeństwo

### ⚠️ Ważne zasady:
1. **Nigdy nie udostępniaj kluczy prywatnych**
2. **Nie commituj secrets do repozytorium**
3. **Regularnie rotuj klucze SSH**
4. **Używaj różnych kluczy dla różnych celów**

### 🔒 Dodatkowe zabezpieczenia:
```bash
# Ogranicz dostęp do klucza SSH
chmod 600 ~/.ssh/github-actions
chmod 600 ~/.ssh/github-actions.pub

# Sprawdź uprawnienia
ls -la ~/.ssh/
```

## 🔄 Rotacja kluczy

### Kiedy rotować klucze?
- Co 6-12 miesięcy
- Po podejrzeniu kompromitacji
- Po zmianie zespołu

### Jak rotować?
1. Wygeneruj nowy klucz SSH
2. Dodaj nowy klucz publiczny do `authorized_keys`
3. Zaktualizuj `VPS_SSH_KEY` w GitHub Secrets
4. Usuń stary klucz z `authorized_keys`
5. Usuń stare pliki kluczy

## 🧪 Test konfiguracji

### Automatyczny test
Po skonfigurowaniu secrets, wykonaj:

1. **Commit i push do main branch**:
```bash
git add .
git commit -m "Test deployment configuration"
git push origin main
```

2. **Sprawdź GitHub Actions**:
   - Przejdź do zakładki **Actions** w repozytorium
   - Sprawdź czy workflow "Deploy to VPS" się wykonuje
   - Sprawdź logi w przypadku błędów

### Ręczny test SSH
```bash
# Test połączenia (na lokalnej maszynie)
ssh -i ~/.ssh/github-actions ubuntu@YOUR_VPS_IP "echo 'SSH connection successful'"
```

## 🆘 Rozwiązywanie problemów

### Błąd: "Permission denied (publickey)"
1. Sprawdź czy klucz publiczny jest w `authorized_keys`
2. Sprawdź uprawnienia plików SSH (600 dla kluczy, 700 dla ~/.ssh)
3. Sprawdź czy klucz prywatny w GitHub Secrets jest kompletny

### Błąd: "Host key verification failed"
1. Dodaj host do known_hosts:
```bash
ssh-keyscan -H YOUR_VPS_IP >> ~/.ssh/known_hosts
```

### Błędy Supabase
1. Sprawdź czy URL i klucz są poprawne
2. Sprawdź czy projekt Supabase jest aktywny
3. Sprawdź limity API w Supabase Dashboard

## 📞 Wsparcie

Jeśli masz problemy z konfiguracją:

1. **Sprawdź logi GitHub Actions** - szczegółowe informacje o błędach
2. **Sprawdź dokumentację** - `docs/deployment-guide.md`
3. **Test lokalny** - uruchom deployment lokalnie
4. **Sprawdź połączenie SSH** - test ręczny

---

**Pamiętaj**: Secrets są wrażliwe! Nigdy nie udostępniaj ich publicznie.
