# 🔧 Troubleshooting - Klimatyzacja SMS Manager

## 🚨 Najczęstsze Problemy

### 1. Aplikacja nie startuje

#### Objawy:
- PM2 pokazuje status "errored" lub "stopped"
- <PERSON><PERSON> odpowiedzi na http://localhost:3000
- B<PERSON><PERSON>dy w logach PM2

#### Rozwiązania:

**Krok 1: Sprawdź logi**
```bash
pm2 logs klimatyzacja-sms-manager --lines 50
```

**Krok 2: Sprawdź zmienne środowiskowe**
```bash
cat .env
# Upewnij się, że wszystkie wymagane zmienne są ustawione
```

**Krok 3: Sprawdź czy build istnieje**
```bash
ls -la dist/
# Jeśli brak katalogu dist/, wykonaj:
npm run build
```

**Krok 4: Restart aplikacji**
```bash
pm2 restart klimatyzacja-sms-manager
# lub
pm2 delete klimatyzacja-sms-manager
pm2 start ecosystem.config.js
```

### 2. Nginx zwraca 502 Bad Gateway

#### Objawy:
- Strona pokazuje "502 Bad Gateway"
- Nginx działa, ale nie może połączyć się z aplikacją

#### Rozwiązania:

**Krok 1: Sprawdź czy aplikacja działa**
```bash
pm2 status
curl http://localhost:3000
```

**Krok 2: Sprawdź konfigurację Nginx**
```bash
sudo nginx -t
sudo tail -f /var/log/nginx/error.log
```

**Krok 3: Sprawdź porty**
```bash
netstat -tlnp | grep :3000
# Aplikacja powinna nasłuchiwać na porcie 3000
```

### 3. SSL nie działa

#### Objawy:
- Certyfikat wygasł
- Błąd "Your connection is not private"
- HTTPS nie działa

#### Rozwiązania:

**Krok 1: Sprawdź status certyfikatu**
```bash
sudo certbot certificates
```

**Krok 2: Odnów certyfikat**
```bash
sudo certbot renew
sudo systemctl reload nginx
```

**Krok 3: Sprawdź konfigurację SSL w Nginx**
```bash
sudo nginx -t
grep -n ssl /etc/nginx/sites-available/klimatyzacja-sms-manager
```

### 4. Wysokie użycie zasobów

#### Objawy:
- Serwer działa wolno
- Aplikacja się zawiesza
- Błędy "out of memory"

#### Rozwiązania:

**Krok 1: Sprawdź użycie zasobów**
```bash
htop
df -h
free -h
```

**Krok 2: Sprawdź logi aplikacji**
```bash
pm2 monit
pm2 logs klimatyzacja-sms-manager --lines 100 | grep -i error
```

**Krok 3: Restart aplikacji**
```bash
pm2 restart klimatyzacja-sms-manager
```

**Krok 4: Optymalizacja PM2**
```bash
# Zmniejsz liczbę instancji jeśli używasz cluster mode
pm2 scale klimatyzacja-sms-manager 1
```

### 5. Problemy z bazą danych Supabase

#### Objawy:
- Błędy połączenia z bazą danych
- "Invalid API key" lub podobne błędy
- Dane nie są zapisywane/odczytywane

#### Rozwiązania:

**Krok 1: Sprawdź zmienne środowiskowe**
```bash
grep SUPABASE .env
```

**Krok 2: Test połączenia**
```bash
curl -I https://your-project.supabase.co
```

**Krok 3: Sprawdź klucze API w Supabase Dashboard**
- Przejdź do Settings > API
- Sprawdź czy klucze są aktualne
- Sprawdź czy projekt jest aktywny

### 6. GitHub Actions nie działają

#### Objawy:
- Deployment fails w GitHub Actions
- SSH connection errors
- Build errors

#### Rozwiązania:

**Krok 1: Sprawdź GitHub Secrets**
```
VPS_HOST - IP serwera
VPS_USER - nazwa użytkownika (np. ubuntu)
VPS_SSH_KEY - klucz prywatny SSH
VITE_SUPABASE_URL - URL Supabase
VITE_SUPABASE_ANON_KEY - klucz Supabase
```

**Krok 2: Test połączenia SSH**
```bash
# Na lokalnej maszynie
ssh -i ~/.ssh/your-key ubuntu@your-server-ip
```

**Krok 3: Sprawdź logi GitHub Actions**
- Przejdź do zakładki Actions w repozytorium
- Sprawdź szczegóły nieudanego workflow

## 🔍 Narzędzia Diagnostyczne

### Skrypt diagnostyczny
```bash
#!/bin/bash
echo "=== DIAGNOSTYKA SYSTEMU ==="
echo "Data: $(date)"
echo ""

echo "=== STATUS APLIKACJI ==="
pm2 status

echo ""
echo "=== STATUS NGINX ==="
systemctl status nginx --no-pager

echo ""
echo "=== UŻYCIE ZASOBÓW ==="
echo "Dysk: $(df -h / | awk 'NR==2 {print $5}')"
echo "RAM: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"

echo ""
echo "=== PORTY ==="
netstat -tlnp | grep -E ':(80|443|3000)'

echo ""
echo "=== OSTATNIE BŁĘDY ==="
echo "PM2 errors:"
pm2 logs klimatyzacja-sms-manager --lines 10 --nostream | grep -i error | tail -5

echo ""
echo "Nginx errors:"
sudo tail -5 /var/log/nginx/error.log

echo ""
echo "=== CERTYFIKAT SSL ==="
if [ -f "/etc/letsencrypt/live/*/cert.pem" ]; then
    CERT_FILE=$(find /etc/letsencrypt/live -name "cert.pem" | head -1)
    openssl x509 -enddate -noout -in "$CERT_FILE"
else
    echo "Brak certyfikatu SSL"
fi
```

### Monitoring w czasie rzeczywistym
```bash
# Terminal 1: Logi aplikacji
pm2 logs klimatyzacja-sms-manager --lines 0

# Terminal 2: Logi Nginx
sudo tail -f /var/log/nginx/access.log /var/log/nginx/error.log

# Terminal 3: Monitoring zasobów
watch -n 1 'free -h && echo "" && df -h && echo "" && uptime'
```

## 📞 Procedury Awaryjne

### Przywracanie z backupu
```bash
# Zatrzymaj aplikację
pm2 stop klimatyzacja-sms-manager

# Przywróć z najnowszego backupu
cd /home/<USER>/backups
LATEST_BACKUP=$(ls -t app_backup_*.tar.gz | head -1)
cd /home/<USER>/klimatyzacja-sms-manager
tar -xzf "/home/<USER>/backups/$LATEST_BACKUP"

# Restart aplikacji
pm2 start ecosystem.config.js
```

### Rollback do poprzedniej wersji
```bash
# Sprawdź dostępne wersje
git log --oneline -10

# Rollback do poprzedniego commita
git reset --hard HEAD~1
npm run build
pm2 restart klimatyzacja-sms-manager
```

### Restart całego systemu
```bash
# Restart wszystkich usług
sudo systemctl restart nginx
pm2 restart all

# W przypadku problemów - restart serwera
sudo reboot
```

## 📋 Checklist Rozwiązywania Problemów

- [ ] Sprawdź logi aplikacji (`pm2 logs`)
- [ ] Sprawdź logi Nginx (`/var/log/nginx/error.log`)
- [ ] Sprawdź użycie zasobów (`htop`, `df -h`)
- [ ] Sprawdź status usług (`systemctl status`)
- [ ] Sprawdź konfigurację (`nginx -t`)
- [ ] Sprawdź zmienne środowiskowe (`.env`)
- [ ] Sprawdź połączenie z bazą danych
- [ ] Sprawdź certyfikat SSL
- [ ] Sprawdź firewall (`ufw status`)
- [ ] Sprawdź porty (`netstat -tlnp`)

## 🆘 Kontakt w Nagłych Przypadkach

1. **Sprawdź monitoring**: `./scripts/monitoring.sh status`
2. **Uruchom diagnostykę**: Użyj skryptu diagnostycznego powyżej
3. **Sprawdź dokumentację**: `docs/deployment-guide.md`
4. **Backup**: `./scripts/backup.sh` (przed większymi zmianami)

---

**Pamiętaj**: Zawsze rób backup przed wprowadzaniem zmian!
